#!/usr/bin/env python3
"""
Integrated Zillow Scraping & CRM System with Multi-threading
Features:
1. Continuous scraping with CSV updates
2. Duplicate checking
3. Infinite scroll for more properties
4. Pipedrive integration (On Market stage)
5. SMS gateway integration
6. Response tracking and auto-response
7. Message tracking to prevent duplicates
"""

import csv
import requests
import time
import threading
import queue
import json
import urllib.parse
import os
from bs4 import BeautifulSoup
import re
from datetime import datetime
import logging
from message_manager import MessageManager
from pipedrive_rate_limiter import PipedriveRateLimiter
from config import PIPEDRIVE_STAGES

# Configuration
SCRAPER_API_KEY = '91b405af27e201a62e346fefbdca9664'
PIPEDRIVE_TOKEN = '****************************************'
SMS_API_TOKEN = '619bfcdb57e507d6fa1c96f836db7215b454af9d'
SMS_API_URL = 'https://humilix.com/sms/services/send.php'
PIPEDRIVE_PIPELINE_ID = '9'
CLIENT_PHONE = '+13214304820'  # Your client's phone number

# Headers
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

# Setup logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zillow_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Configure console handler to handle Unicode properly
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# Set encoding for console output
import sys
if hasattr(sys.stdout, 'reconfigure'):
    try:
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except:
        pass

# Safe logging function for Unicode characters
def safe_log(level, message):
    """Safely log messages with Unicode characters"""
    try:
        if level == 'info':
            logging.info(message)
        elif level == 'error':
            logging.error(message)
        elif level == 'warning':
            logging.warning(message)
        elif level == 'debug':
            logging.debug(message)
    except UnicodeEncodeError:
        # Fallback: Remove emojis and special characters
        safe_message = message.encode('ascii', 'ignore').decode('ascii')
        if level == 'info':
            logging.info(safe_message)
        elif level == 'error':
            logging.error(safe_message)
        elif level == 'warning':
            logging.warning(safe_message)
        elif level == 'debug':
            logging.debug(safe_message)

class ZillowIntegratedSystem:
    def __init__(self):
        self.property_queue = queue.Queue()
        self.sms_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.existing_properties = set()
        self.messaged_agents = set()
        self.running = True

        # Initialize message manager and rate limiter
        self.message_manager = MessageManager()
        self.rate_limiter = PipedriveRateLimiter()

        # Initialize multi-device manager
        from multi_device_manager import MultiDeviceManager
        self.device_manager = MultiDeviceManager()

        # Initialize session tracking for sent messages
        self.session_sent_messages = {}

        # Track processed CSV properties to avoid duplicates in Pipedrive
        self.processed_csv_properties = set()

        # Load existing data
        self.load_existing_data()

        # Initialize processed CSV properties from existing CSV
        self.initialize_processed_csv_properties()

        # Process existing CSV properties to Pipedrive on startup
        self.process_existing_csv_on_startup()

        # Get message template from manager
        self.message_template = self.message_manager.get_template(
            self.message_manager.config.get('DEFAULT_TEMPLATE', 'INITIAL_CONTACT')
        )

        # Auto response template
        self.auto_response = self.message_manager.get_template('AUTO_RESPONSE')
    
    def load_existing_data(self):
        """Load existing properties and messaged agents"""
        try:
            with open('zillow_florida_data.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    url = row.get('URL', '').strip()
                    if url:
                        self.existing_properties.add(url)

                    # Track messaged agents
                    agent_phone = row.get('Agent_Phone', '').strip()
                    if agent_phone and agent_phone != 'N/A':
                        self.messaged_agents.add(agent_phone)

            logging.info(f"Loaded {len(self.existing_properties)} existing properties")
            logging.info(f"Loaded {len(self.messaged_agents)} messaged agents")

        except FileNotFoundError:
            logging.info("No existing CSV found, starting fresh")

    def initialize_processed_csv_properties(self):
        """Initialize processed CSV properties to avoid re-processing existing data"""
        try:
            if not os.path.exists('zillow_florida_data.csv'):
                return

            with open('zillow_florida_data.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)

                for row in reader:
                    # Create same unique identifier used in CSV processing
                    property_id = f"{row.get('Address', '')}_{row.get('Price', '')}"

                    # Mark as already processed
                    if property_id and property_id != "_":
                        self.processed_csv_properties.add(property_id)

            safe_log('info', f"📊 Initialized {len(self.processed_csv_properties)} existing CSV properties as processed")

        except Exception as e:
            logging.error(f"Error initializing processed CSV properties: {str(e)}")

    def process_existing_csv_on_startup(self):
        """Process existing CSV properties to Pipedrive on system startup"""
        try:
            safe_log('info', "🚀 Processing existing CSV properties to Pipedrive on startup...")

            # Clear processed properties to force processing all existing data
            original_processed = self.processed_csv_properties.copy()
            self.processed_csv_properties.clear()

            # Process all CSV properties
            processed_count = self.process_csv_for_pipedrive(force_process_all=True)

            # Restore processed properties set for future monitoring
            self.processed_csv_properties = original_processed

            if processed_count and processed_count > 0:
                safe_log('info', f"✅ Processed {processed_count} existing CSV properties to Pipedrive on startup")
            else:
                safe_log('info', "📊 No new properties added to Pipedrive (may already exist)")

        except Exception as e:
            logging.error(f"Error processing existing CSV on startup: {str(e)}")


    
    def normalize_phone(self, phone):
        """Normalize phone number format"""
        if not phone or phone == 'N/A':
            return None
        
        # Remove all non-digits
        digits = re.sub(r'\D', '', phone)
        
        # Add +1 if US number
        if len(digits) == 10:
            return f"+1{digits}"
        elif len(digits) == 11 and digits.startswith('1'):
            return f"+{digits}"
        
        return None
    
    def fetch_properties_with_scroll(self, page=1, max_pages=50):
        """Fetch properties with infinite scroll simulation"""
        logging.info(f"🔍 Fetching properties from page {page}")
        
        try:
            # Build search URL
            search_params = {
                "searchQueryState": json.dumps({
                    "pagination": {"currentPage": page},
                    "usersSearchTerm": "Florida",
                    "mapBounds": {
                        "west": -96.44986467187502,
                        "east": -71.15933732812502,
                        "south": 20.393022319423494,
                        "north": 34.639525177290345
                    },
                    "mapZoom": 6,
                    "filterState": {
                        "price": {"min": 100000, "max": 400000},
                        "mp": {"min": 504, "max": 2015},
                        "beds": {"min": 2},
                        "baths": {"min": 2},
                        "gar": {"value": True},
                        "sqft": {"min": 1000},
                        "lot": {"min": 0, "max": 43560},
                        "built": {"min": 1970},
                        "55plus": {"value": "e"},
                        "att": {"value": "AS-IS"}
                    },
                    "isListVisible": True,
                    "category": "cat1",
                    "regionSelection": [{"regionId": 14, "regionType": 2}]
                })
            }
            
            base_url = f"https://www.zillow.com/fl/{page}_p/"
            url = f"{base_url}?{urllib.parse.urlencode(search_params)}"
            
            # Use ScraperAPI
            scraper_url = f"https://api.scraperapi.com/?api_key={SCRAPER_API_KEY}&url={urllib.parse.quote(url)}"
            
            response = requests.get(scraper_url, headers=HEADERS, timeout=90)
            
            if response.status_code != 200:
                logging.error(f"Failed to fetch page {page}: HTTP {response.status_code}")
                return []
            
            # Parse properties from JSON
            properties = self.parse_properties_from_response(response.text)
            
            logging.info(f"✅ Found {len(properties)} properties on page {page}")
            
            # Continue to next page if properties found and not at max
            if properties and page < max_pages:
                # Add delay between pages
                time.sleep(3)
                # Recursively fetch next page
                next_properties = self.fetch_properties_with_scroll(page + 1, max_pages)
                properties.extend(next_properties)
            
            return properties
            
        except Exception as e:
            logging.error(f"Error fetching page {page}: {str(e)}")
            return []
    
    def parse_properties_from_response(self, html_content):
        """Parse properties from HTML response"""
        properties = []
        
        try:
            # Look for JSON data in script tags
            soup = BeautifulSoup(html_content, 'html.parser')
            scripts = soup.find_all('script')
            
            for script in scripts:
                if script.string and 'searchResults' in script.string:
                    # Extract JSON data
                    json_match = re.search(r'{"searchResults".*?"pagination".*?}', script.string)
                    if json_match:
                        try:
                            data = json.loads(json_match.group())
                            results = data.get('searchResults', {}).get('listResults', [])
                            
                            for result in results:
                                prop = self.extract_property_from_json(result)
                                if prop:
                                    properties.append(prop)
                                    
                        except json.JSONDecodeError:
                            continue
            
            # Fallback: Parse HTML directly
            if not properties:
                properties = self.parse_properties_from_html(soup)
            
        except Exception as e:
            logging.error(f"Error parsing properties: {str(e)}")
        
        return properties
    
    def extract_property_from_json(self, result):
        """Extract property data from JSON result"""
        try:
            address = result.get('address', '')
            price = result.get('price', '')
            beds = result.get('beds', '')
            baths = result.get('baths', '')
            area = result.get('area', '')
            detail_url = result.get('detailUrl', '')
            
            if detail_url and detail_url.startswith('/'):
                detail_url = f"https://www.zillow.com{detail_url}"
            
            return {
                'address': address,
                'price': price,
                'beds': f"{beds}bd" if beds else 'N/A',
                'baths': f"{baths}ba" if baths else 'N/A',
                'sqft': f"{area}sqft" if area else 'N/A',
                'url': detail_url
            }
            
        except Exception as e:
            logging.error(f"Error extracting property from JSON: {str(e)}")
            return None
    
    def parse_properties_from_html(self, soup):
        """Fallback HTML parsing"""
        properties = []
        
        try:
            # Look for property cards
            property_cards = soup.find_all(['article', 'div'], class_=re.compile(r'property|listing|result', re.I))
            
            for card in property_cards[:10]:  # Limit to prevent overload
                try:
                    # Extract basic info
                    address_elem = card.find(['a', 'span'], string=re.compile(r'\w+\s+\w+.*FL', re.I))
                    price_elem = card.find(['span', 'div'], string=re.compile(r'\$[\d,]+'))
                    
                    if address_elem and price_elem:
                        address = address_elem.get_text(strip=True)
                        price = price_elem.get_text(strip=True)
                        
                        # Find detail URL
                        link_elem = card.find('a', href=re.compile(r'/homedetails/'))
                        detail_url = ''
                        if link_elem:
                            href = link_elem.get('href')
                            if href.startswith('/'):
                                detail_url = f"https://www.zillow.com{href}"
                            else:
                                detail_url = href
                        
                        properties.append({
                            'address': address,
                            'price': price,
                            'beds': 'N/A',
                            'baths': 'N/A',
                            'sqft': 'N/A',
                            'url': detail_url
                        })
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            logging.error(f"Error parsing HTML: {str(e)}")
        
        return properties
    
    def scraping_worker(self):
        """Main scraping worker thread"""
        logging.info("🚀 Starting scraping worker")
        
        page = 1
        while self.running:
            try:
                # Fetch properties with scroll
                properties = self.fetch_properties_with_scroll(page, max_pages=10)
                
                new_properties = 0
                for prop in properties:
                    url = prop.get('url', '')
                    
                    # Check for duplicates
                    if url and url not in self.existing_properties:
                        # Add to queue for processing
                        self.property_queue.put(prop)
                        self.existing_properties.add(url)
                        new_properties += 1
                
                logging.info(f"📊 Page {page}: {new_properties} new properties added to queue")
                
                # Move to next batch
                page += 10
                
                # Rest between batches
                time.sleep(30)
                
            except Exception as e:
                logging.error(f"Error in scraping worker: {str(e)}")
                time.sleep(60)
    
    def property_processor_worker(self):
        """Process properties and extract details"""
        logging.info("🔧 Starting property processor worker")
        
        while self.running:
            try:
                # Get property from queue
                prop = self.property_queue.get(timeout=30)
                
                # Extract full details
                full_prop = self.extract_full_property_details(prop)
                
                if full_prop:
                    # Save to CSV
                    self.save_property_to_csv(full_prop)
                    
                    # Add to Pipedrive
                    self.add_to_pipedrive(full_prop)
                    
                    # Add to SMS queue if agent info available
                    if (full_prop.get('Agent_Phone') and 
                        full_prop.get('Agent_Phone') != 'N/A' and
                        full_prop.get('Agent_Phone') not in self.messaged_agents):
                        self.sms_queue.put(full_prop)
                
                self.property_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error in property processor: {str(e)}")
    
    def extract_full_property_details(self, prop):
        """Extract full property details including agent info"""
        url = prop.get('url', '')
        if not url:
            return None
        
        try:
            # Use enhanced extraction logic
            scraper_url = f"https://api.scraperapi.com/?api_key={SCRAPER_API_KEY}&url={urllib.parse.quote(url)}"
            response = requests.get(scraper_url, headers=HEADERS, timeout=60)
            
            if response.status_code != 200:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract all property details from soup
            property_details = self.extract_property_facts(soup)
            agent_info = self.extract_agent_info(soup, response.text)  # Pass response text for JSON patterns
            address_info = self.extract_address_and_price(soup)

            # Merge data with fallbacks
            full_prop = {
                'Address': address_info.get('address') or prop.get('address', 'N/A'),
                'Beds': property_details.get('beds', prop.get('beds', 'N/A')),
                'Baths': property_details.get('baths', prop.get('baths', 'N/A')),
                'Sqft': property_details.get('sqft', prop.get('sqft', 'N/A')),
                'Price': address_info.get('price') or prop.get('price', 'N/A'),
                'Agent_Name': agent_info.get('agent_name', 'N/A'),
                'Agent_Phone': agent_info.get('agent_phone', 'N/A'),
                'URL': url
            }
            
            return full_prop
            
        except Exception as e:
            logging.error(f"Error extracting details for {url}: {str(e)}")
            return None

    def extract_address_and_price(self, soup):
        """Extract address and price from soup"""
        info = {'address': None, 'price': None}

        try:
            # Extract address using multiple selectors
            address_selectors = [
                'h1[data-testid="property-details-address"]',
                '.summary-address',
                'h1.notranslate',
                '[data-testid="property-details-address"]',
                'h1[class*="address"]',
                '.property-address h1'
            ]

            for selector in address_selectors:
                addr_elem = soup.select_one(selector)
                if addr_elem:
                    address_text = addr_elem.get_text(strip=True)
                    if address_text and len(address_text) > 10:  # Valid address
                        info['address'] = address_text
                        break

            # Extract price using multiple selectors
            price_selectors = [
                '[data-testid="price"]',
                '.summary-price',
                '.price-large',
                'span[data-testid="price"]',
                '[class*="price"]',
                '.property-price'
            ]

            for selector in price_selectors:
                price_elem = soup.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    if '$' in price_text:  # Valid price
                        info['price'] = price_text
                        break

        except Exception as e:
            logging.error(f"Error extracting address/price: {str(e)}")

        return info
    
    def extract_property_facts(self, soup):
        """Extract beds, baths, sqft using enhanced logic"""
        details = {'beds': 'N/A', 'baths': 'N/A', 'sqft': 'N/A'}

        try:
            # Method 1: Individual spans for beds
            if details['beds'] == 'N/A':
                bed_spans = soup.find_all('span', string=re.compile(r'^\d{1,2}$'))
                for span in bed_spans:
                    next_span = span.find_next_sibling('span')
                    if next_span and 'bed' in next_span.get_text().lower():
                        beds_num = span.get_text().strip()
                        if 1 <= int(beds_num) <= 20:
                            details['beds'] = f"{beds_num}bd"
                            break

            # Method 1: Individual spans for baths
            if details['baths'] == 'N/A':
                bath_spans = soup.find_all('span', string=re.compile(r'^\d{1,2}$'))
                for span in bath_spans:
                    next_span = span.find_next_sibling('span')
                    if next_span and 'bath' in next_span.get_text().lower():
                        baths_num = span.get_text().strip()
                        if 1 <= int(baths_num) <= 20:
                            details['baths'] = f"{baths_num}ba"
                            break

            # Method 1: Individual spans for sqft
            if details['sqft'] == 'N/A':
                sqft_spans = soup.find_all('span', string=re.compile(r'^[\d,]{3,6}$'))
                for span in sqft_spans:
                    next_span = span.find_next_sibling('span')
                    if next_span and 'sqft' in next_span.get_text().lower():
                        sqft_num = span.get_text().strip()
                        details['sqft'] = f"{sqft_num}sqft"
                        break

            # Method 2: Exact patterns in spans
            if any(v == 'N/A' for v in details.values()):
                all_spans = soup.find_all('span')
                for span in all_spans:
                    text = span.get_text(strip=True)
                    if re.match(r'^\d{1,2}bd$', text.lower()) and details['beds'] == 'N/A':
                        details['beds'] = text
                    elif re.match(r'^\d{1,2}ba$', text.lower()) and details['baths'] == 'N/A':
                        details['baths'] = text
                    elif re.match(r'^[\d,]{3,6}sqft$', text.lower()) and details['sqft'] == 'N/A':
                        details['sqft'] = text

            # Method 3: Text search
            if any(v == 'N/A' for v in details.values()):
                page_text = soup.get_text()

                if details['beds'] == 'N/A':
                    bed_match = re.search(r'\b(\d{1,2})\s*bed', page_text.lower())
                    if bed_match:
                        beds_num = bed_match.group(1)
                        if 1 <= int(beds_num) <= 20:
                            details['beds'] = f"{beds_num}bd"

                if details['baths'] == 'N/A':
                    bath_match = re.search(r'\b(\d{1,2})\s*bath', page_text.lower())
                    if bath_match:
                        baths_num = bath_match.group(1)
                        if 1 <= int(baths_num) <= 20:
                            details['baths'] = f"{baths_num}ba"

                if details['sqft'] == 'N/A':
                    sqft_match = re.search(r'\b([\d,]{3,6})\s*sq', page_text.lower())
                    if sqft_match:
                        sqft_num = sqft_match.group(1).replace(',', '')
                        if 500 <= int(sqft_num) <= 50000:
                            details['sqft'] = f"{sqft_match.group(1)}sqft"

        except Exception as e:
            logging.error(f"Error extracting property facts: {str(e)}")

        return details
    
    def extract_agent_info(self, soup, response_text=None):
        """Enhanced agent information extraction using proven methods"""
        agent = {'agent_name': 'N/A', 'agent_phone': 'N/A'}

        try:
            # Use proven extraction methods from cleaned_zillow_scraper_before.py

            # Method 1: Agent attribution selectors (proven working)
            agent_selectors = [
                '[data-testid="attribution-LISTING_AGENT"]',
                '.agent-name',
                '.listing-agent-name'
            ]

            for selector in agent_selectors:
                agent_elem = soup.select_one(selector)
                if agent_elem:
                    agent_text = agent_elem.get_text(strip=True)
                    if agent_text:
                        # Extract phone number from agent text
                        phone_pattern = r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
                        phone_match = re.search(phone_pattern, agent_text)
                        if phone_match:
                            agent['agent_phone'] = phone_match.group()
                            # Remove phone from name and clean
                            agent_name = re.sub(phone_pattern, '', agent_text).strip().rstrip(',').strip()
                            agent_name = re.sub(r'\d+', '', agent_name).strip().rstrip(',').strip()
                            agent_name = re.sub(r'[,\s]+$', '', agent_name).strip()
                            if agent_name and len(agent_name) > 2:
                                agent['agent_name'] = agent_name
                        else:
                            # Clean agent name from numbers and extra characters
                            clean_name = re.sub(r'\d+', '', agent_text).strip().rstrip(',').strip()
                            clean_name = re.sub(r'[,\s]+$', '', clean_name).strip()
                            if clean_name and len(clean_name) > 2:
                                agent['agent_name'] = clean_name
                        break

            # Method 2: Seller attribution (proven to work)
            if agent['agent_name'] == 'N/A':
                container = soup.find('div', {'data-testid': 'seller-attribution'})
                if container:
                    attribution_elements = container.find_all('p', {'data-testid': re.compile(r'attribution')})
                    if not attribution_elements:
                        attribution_elements = container.find_all('p')

                    for agent_element in attribution_elements:
                        # Method A: Look for 2 spans (standard format)
                        spans = agent_element.find_all('span')
                        if len(spans) >= 2:
                            potential_name = spans[0].get_text(strip=True)
                            potential_phone = spans[1].get_text(strip=True).rstrip(',')

                            if potential_name and len(potential_name) > 1 and len(potential_name) < 100:
                                agent['agent_name'] = potential_name
                                agent['agent_phone'] = potential_phone
                                break

                        # Method B: Look for button + span (Property Owner format)
                        elif len(spans) == 1:
                            button = agent_element.find('button')
                            if button and spans:
                                potential_name = button.get_text(strip=True)
                                potential_phone = spans[0].get_text(strip=True).rstrip(',')

                                if potential_name and len(potential_name) > 1 and len(potential_name) < 100:
                                    agent['agent_name'] = potential_name
                                    agent['agent_phone'] = potential_phone
                                    break

            # Method 3: Contact buttons
            if agent['agent_name'] == 'N/A':
                contact_buttons = soup.find_all('button', string=re.compile(r'Contact.*', re.IGNORECASE))
                for button in contact_buttons:
                    button_text = button.get_text(strip=True)
                    if 'Contact' in button_text and len(button_text) > 8:
                        agent_name = button_text.replace('Contact', '').strip()
                        if agent_name and agent_name not in ['Agent', 'Listing Agent']:
                            agent['agent_name'] = agent_name
                            break

            # Method 4: Enhanced JSON patterns (proven working)
            if (agent['agent_name'] == 'N/A' or agent['agent_phone'] == 'N/A') and response_text:
                json_patterns = [
                    r'"listingAgent":(\{.*?\})',
                    r'"agent":\s*\{[^}]*"name":\s*"([^"]+)"[^}]*"phoneNumber":\s*"([^"]+)"',
                    r'"contactAgent"[^}]*"name":\s*"([^"]+)"[^}]*"phone":\s*"([^"]+)"',
                    r'"listingAgentName":\s*"([^"]+)".*?"listingAgentPhone":\s*"([^"]+)"',
                    r'"contactInfo"[^}]*"name":\s*"([^"]+)"[^}]*"phone":\s*"([^"]+)"',
                    r'"agentContact"[^}]*"displayName":\s*"([^"]+)"[^}]*"phoneNumber":\s*"([^"]+)"',
                    r'"primaryAgent"[^}]*"name":\s*"([^"]+)"[^}]*"phone":\s*"([^"]+)"'
                ]

                for i, pattern in enumerate(json_patterns, 1):
                    match = re.search(pattern, response_text)
                    if match:
                        try:
                            if i == 1:  # listingAgent object
                                agent_json = json.loads(match.group(1))
                                if agent['agent_name'] == 'N/A':
                                    agent['agent_name'] = agent_json.get('name', 'N/A')
                                if agent['agent_phone'] == 'N/A':
                                    agent['agent_phone'] = agent_json.get('phoneNumber', 'N/A')
                            else:  # Direct name/phone patterns
                                if agent['agent_name'] == 'N/A':
                                    agent['agent_name'] = match.group(1)
                                if agent['agent_phone'] == 'N/A' and len(match.groups()) > 1:
                                    agent['agent_phone'] = match.group(2)

                            if agent['agent_name'] != 'N/A' and len(agent['agent_name']) > 2:
                                break
                        except:
                            continue

            # Method 5: Phone number search with context (avoid toll-free)
            if agent['agent_phone'] == 'N/A' and response_text:
                phone_pattern = r'(\(\d{3}\)\s*\d{3}-\d{4}|\d{3}[-.\s]\d{3}[-.\s]\d{4})'
                phone_matches = re.finditer(phone_pattern, response_text)

                for phone_match in phone_matches:
                    phone = phone_match.group(0)
                    # Skip toll-free numbers
                    if not any(x in phone for x in ['800', '888', '877', '866', '844']):
                        agent['agent_phone'] = phone

                        # Look for names near phone number
                        if agent['agent_name'] == 'N/A':
                            start_pos = max(0, phone_match.start() - 200)
                            end_pos = min(len(response_text), phone_match.end() + 200)
                            context = response_text[start_pos:end_pos]

                            name_pattern = r'\b([A-Z][a-z]+\s+[A-Z][a-z]+)\b'
                            name_matches = re.findall(name_pattern, context)

                            for name in name_matches:
                                if not any(word in name.lower() for word in ['contact', 'agent', 'phone', 'call', 'email', 'zillow']):
                                    if len(name) > 5 and len(name) < 50:
                                        agent['agent_name'] = name.strip()
                                        break
                        break

            # Method 6: "Listing Provided by:" pattern
            if agent['agent_name'] == 'N/A' and response_text:
                listing_pattern = r'Listing\s+Provided\s+by[:\s]*([A-Za-z\s]+)\s+(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})'
                listing_match = re.search(listing_pattern, response_text, re.IGNORECASE)
                if listing_match:
                    agent['agent_name'] = listing_match.group(1).strip()
                    agent['agent_phone'] = listing_match.group(2).strip()

        except Exception as e:
            logging.error(f"Error extracting agent info: {str(e)}")

        return agent









    def save_property_to_csv(self, prop):
        """Save property to CSV file"""
        try:
            headers = ['Address', 'Beds', 'Baths', 'Sqft', 'Price', 'Agent_Name', 'Agent_Phone', 'URL']

            # Check if file exists
            file_exists = False
            try:
                with open('zillow_florida_data.csv', 'r'):
                    file_exists = True
            except FileNotFoundError:
                pass

            # Append to CSV
            with open('zillow_florida_data.csv', 'a', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=headers)

                if not file_exists:
                    writer.writeheader()

                writer.writerow(prop)

            logging.info(f"💾 Saved property: {prop.get('Address', '')}")

        except Exception as e:
            logging.error(f"Error saving to CSV: {str(e)}")

    def add_to_pipedrive(self, prop):
        """Add property to Pipedrive On Market stage with rate limiting"""
        try:
            # Check rate limit before making request
            if not self.rate_limiter.wait_for_availability('create_deal', max_wait=60):
                logging.error("❌ Rate limit exceeded, skipping Pipedrive add")
                return None

            # Prepare deal data - sirf basic fields (person_name, org_name, notes separately add karenge)
            deal_data = {
                'title': f"{prop.get('Address', '')} - {prop.get('Price', '')}",
                'value': self.extract_price_value(prop.get('Price', '')),
                'currency': 'USD',
                'pipeline_id': PIPEDRIVE_PIPELINE_ID,
                'stage_id': PIPEDRIVE_STAGES['LEAD_IN']  # Lead In stage (101)
            }

            # Add to Pipedrive
            url = f"https://api.pipedrive.com/v1/deals?api_token={PIPEDRIVE_TOKEN}"
            response = requests.post(url, json=deal_data)

            # Record the API request
            self.rate_limiter.record_request('create_deal')

            if response.status_code == 201:
                deal_id = response.json().get('data', {}).get('id')
                logging.info(f"✅ Added to Pipedrive: {prop.get('Address', '')} (ID: {deal_id})")

                # Ab notes, person aur organization add karte hain
                self.add_property_notes_to_deal(deal_id, prop)
                self.add_person_to_deal(deal_id, prop)
                self.add_organization_to_deal(deal_id, prop)

                return deal_id
            else:
                logging.error(f"❌ Pipedrive error: {response.status_code}")
                logging.error(f"Response: {response.text}")
                return None

        except Exception as e:
            logging.error(f"Error adding to Pipedrive: {str(e)}")

    def add_property_notes_to_deal(self, deal_id, prop):
        """Deal mein property notes add karta hai"""
        try:
            if not self.rate_limiter.wait_for_availability('add_note', max_wait=30):
                logging.warning("❌ Rate limit exceeded, skipping notes")
                return False

            notes_content = f"""
🏠 PROPERTY DETAILS

Address: {prop.get('Address', '')}
Price: {prop.get('Price', '')}
Beds: {prop.get('Beds', '')}
Baths: {prop.get('Baths', '')}
Sqft: {prop.get('Sqft', '')}
Agent: {prop.get('Agent_Name', '')}
Phone: {prop.get('Agent_Phone', '')}
URL: {prop.get('URL', '')}
            """.strip()

            note_data = {
                'content': notes_content,
                'deal_id': deal_id
            }

            url = f"https://api.pipedrive.com/v1/notes?api_token={PIPEDRIVE_TOKEN}"
            response = requests.post(url, json=note_data)

            # Record the API request
            self.rate_limiter.record_request('add_note')

            if response.status_code == 201:
                logging.info(f"✅ Added property notes to deal {deal_id}")
                return True
            else:
                logging.warning(f"⚠️ Failed to add notes: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"Error adding property notes: {str(e)}")
            return False

    def add_person_to_deal(self, deal_id, prop):
        """Deal mein person add karta hai"""
        try:
            if not self.rate_limiter.wait_for_availability('add_person', max_wait=30):
                logging.warning("❌ Rate limit exceeded, skipping person")
                return False

            # Pehle person create karta hun
            person_data = {
                'name': prop.get('Agent_Name', 'Unknown Agent'),
                'phone': [prop.get('Agent_Phone', '')],
                'org_id': None  # Organization baad mein link karenge
            }

            url = f"https://api.pipedrive.com/v1/persons?api_token={PIPEDRIVE_TOKEN}"
            response = requests.post(url, json=person_data)

            self.rate_limiter.record_request('add_person')

            if response.status_code == 201:
                person_id = response.json().get('data', {}).get('id')
                logging.info(f"✅ Created person: {prop.get('Agent_Name', '')} (ID: {person_id})")

                # Ab deal ko person se link karta hun
                deal_update_data = {'person_id': person_id}
                update_url = f"https://api.pipedrive.com/v1/deals/{deal_id}?api_token={PIPEDRIVE_TOKEN}"
                update_response = requests.put(update_url, json=deal_update_data)

                self.rate_limiter.record_request('update_deal')

                if update_response.status_code == 200:
                    logging.info(f"✅ Linked person to deal {deal_id}")
                    return person_id
                else:
                    logging.warning(f"⚠️ Failed to link person to deal: {update_response.status_code}")
                    return person_id
            else:
                logging.warning(f"⚠️ Failed to create person: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"Error adding person: {str(e)}")
            return False

    def add_organization_to_deal(self, deal_id, prop):
        """Deal mein organization add karta hai"""
        try:
            if not self.rate_limiter.wait_for_availability('add_org', max_wait=30):
                logging.warning("❌ Rate limit exceeded, skipping organization")
                return False

            # Organization create karta hun
            org_data = {
                'name': 'Zillow Property'
            }

            url = f"https://api.pipedrive.com/v1/organizations?api_token={PIPEDRIVE_TOKEN}"
            response = requests.post(url, json=org_data)

            self.rate_limiter.record_request('add_org')

            if response.status_code == 201:
                org_id = response.json().get('data', {}).get('id')
                logging.info(f"✅ Created organization: Zillow Property (ID: {org_id})")

                # Ab deal ko organization se link karta hun
                deal_update_data = {'org_id': org_id}
                update_url = f"https://api.pipedrive.com/v1/deals/{deal_id}?api_token={PIPEDRIVE_TOKEN}"
                update_response = requests.put(update_url, json=deal_update_data)

                self.rate_limiter.record_request('update_deal')

                if update_response.status_code == 200:
                    logging.info(f"✅ Linked organization to deal {deal_id}")
                    return org_id
                else:
                    logging.warning(f"⚠️ Failed to link organization to deal: {update_response.status_code}")
                    return org_id
            else:
                logging.warning(f"⚠️ Failed to create organization: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"Error adding organization: {str(e)}")
            return False

    def extract_price_value(self, price_str):
        """Extract numeric value from price string"""
        try:
            if price_str:
                # Remove $ and commas, extract number
                numbers = re.findall(r'[\d,]+', price_str.replace('$', ''))
                if numbers:
                    return int(numbers[0].replace(',', ''))
        except:
            pass
        return 0

    def sms_worker(self):
        """SMS sending worker thread"""
        logging.info("📱 Starting SMS worker")

        while self.running:
            try:
                # Get property from SMS queue
                prop = self.sms_queue.get(timeout=30)

                agent_phone = prop.get('Agent_Phone', '')

                if agent_phone and agent_phone != 'N/A':
                    # Normalize phone number
                    normalized_phone = self.normalize_phone(agent_phone)

                    if normalized_phone and normalized_phone not in self.messaged_agents:
                        # Send SMS
                        success = self.send_sms(prop, normalized_phone)

                        if success:
                            # Mark as messaged
                            self.messaged_agents.add(normalized_phone)

                            # Update Pipedrive with message note
                            self.update_pipedrive_with_message(prop, normalized_phone)

                self.sms_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error in SMS worker: {str(e)}")

    def send_sms(self, prop, phone):
        """Send SMS to agent using GET method with custom message"""
        try:
            # Format message using message manager
            message = self.message_manager.format_message(
                self.message_manager.config.get('DEFAULT_TEMPLATE', 'INITIAL_CONTACT'),
                agent_name=prop.get('Agent_Name', 'Agent'),
                address=prop.get('Address', ''),
                price=prop.get('Price', ''),
                beds=prop.get('Beds', ''),
                baths=prop.get('Baths', ''),
                sqft=prop.get('Sqft', ''),
                sender_name='Buyer'
            )

            # Get best device for this target using multi-device manager
            selected_device = self.device_manager.get_best_device_for_target(phone)

            if not selected_device:
                logging.error(f"❌ No available device for SMS to {phone}")
                return False

            device_config = self.device_manager.get_device_config_string(selected_device)
            sender_phone = self.device_manager.get_sender_phone(selected_device)

            logging.info(f"📱 Using device {device_config} ({sender_phone}) for {phone}")

            # SMS API parameters - Multi-device support
            sms_params = {
                'key': SMS_API_TOKEN,
                'number': phone,
                'message': message,
                'devices': device_config,  # Dynamic device selection
                'type': 'sms',
                'prioritize': '0'
            }

            # Log message details
            logging.info(f"📱 Sending SMS from {self.message_manager.sender_phone} to {phone}")
            logging.debug(f"💬 Message: {message[:100]}...")

            # Send SMS using POST request (like working demo)
            response = requests.post(SMS_API_URL, data=sms_params, timeout=30)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        message_id = result.get('data', {}).get('messages', [{}])[0].get('ID')
                        device_id = result.get('data', {}).get('messages', [{}])[0].get('deviceID')
                        status = result.get('data', {}).get('messages', [{}])[0].get('status')

                        logging.info(f"📱 SMS sent to {phone} for {prop.get('Address', '')} - ID: {message_id}, Device: {device_id}, Status: {status}")

                        # Mark device as used in multi-device manager
                        self.device_manager.mark_device_used(selected_device)

                        # Track sent message for response monitoring
                        self.track_sent_message(prop, phone, message_id)

                        # Update deal to "Contact Attempted" stage only if SMS actually sent
                        self.update_deal_stage_after_sms(prop, phone)

                        return True
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        logging.error(f"❌ SMS failed: {error_msg}")
                        return False
                except json.JSONDecodeError:
                    logging.warning(f"⚠️ SMS response not JSON, assuming success")
                    self.update_deal_stage_after_sms(prop, phone)
                    return True
            else:
                logging.error(f"❌ SMS failed: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logging.error(f"Error sending SMS: {str(e)}")
            return False

    def update_deal_stage_after_sms(self, prop, phone):
        """Update deal to Contact Attempted stage after SMS sent"""
        try:
            # Find deal by address
            search_url = f"https://api.pipedrive.com/v1/deals/search?term={urllib.parse.quote(prop.get('Address', ''))}&api_token={PIPEDRIVE_TOKEN}"
            search_response = requests.get(search_url)

            if search_response.status_code == 200:
                search_data = search_response.json()
                deals = search_data.get('data', {}).get('items', [])

                if deals:
                    deal_id = deals[0].get('item', {}).get('id')

                    # Update to Contact Attempted stage (stage 102)
                    update_data = {
                        'stage_id': PIPEDRIVE_STAGES['CONTACT_ATTEMPTED']  # Contact Attempted stage (102)
                    }

                    update_url = f"https://api.pipedrive.com/v1/deals/{deal_id}?api_token={PIPEDRIVE_TOKEN}"
                    update_response = requests.put(update_url, json=update_data)

                    if update_response.status_code == 200:
                        logging.info(f"✅ Deal moved to Contact Attempted stage for {prop.get('Address', '')}")

        except Exception as e:
            logging.error(f"Error updating deal stage after SMS: {str(e)}")

    def update_pipedrive_with_message(self, prop, phone):
        """Update Pipedrive deal with message note"""
        try:
            # Find deal by title
            search_url = f"https://api.pipedrive.com/v1/deals/search?term={urllib.parse.quote(prop.get('Address', ''))}&api_token={PIPEDRIVE_TOKEN}"
            search_response = requests.get(search_url)

            if search_response.status_code == 200:
                search_data = search_response.json()
                deals = search_data.get('data', {}).get('items', [])

                if deals:
                    deal_id = deals[0].get('item', {}).get('id')

                    # Add note
                    note_data = {
                        'content': f"""
📱 SMS SENT

Agent: {prop.get('Agent_Name', '')}
Phone: {phone}
Property: {prop.get('Address', '')}
Sent: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Message Sent:
{self.message_template.format(
    agent_name=prop.get('Agent_Name', 'Agent'),
    address=prop.get('Address', ''),
    price=prop.get('Price', '')
)}
                        """.strip(),
                        'deal_id': deal_id
                    }

                    note_url = f"https://api.pipedrive.com/v1/notes?api_token={PIPEDRIVE_TOKEN}"
                    note_response = requests.post(note_url, json=note_data)

                    if note_response.status_code == 201:
                        logging.info(f"✅ Added SMS note to Pipedrive for {prop.get('Address', '')}")

        except Exception as e:
            logging.error(f"Error updating Pipedrive with message: {str(e)}")

    def csv_monitor_worker(self):
        """Monitor CSV file and add new properties to Pipedrive"""
        logging.info("📊 Starting CSV monitor worker")

        while self.running:
            try:
                # Read current CSV and check for new properties
                self.process_csv_for_pipedrive()

                # Wait before next check
                time.sleep(60)  # Check every minute

            except Exception as e:
                logging.error(f"Error in CSV monitor: {str(e)}")
                time.sleep(60)

    def process_csv_for_pipedrive(self, force_process_all=False):
        """Process CSV file and add new properties to Pipedrive"""
        try:
            if not os.path.exists('zillow_florida_data.csv'):
                return

            with open('zillow_florida_data.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                new_properties_count = 0

                for row in reader:
                    try:
                        # Create unique identifier for property
                        property_id = f"{row.get('Address', '')}_{row.get('Price', '')}"

                        # Skip if already processed (unless force processing)
                        if not force_process_all and property_id in self.processed_csv_properties:
                            continue

                        # Convert CSV row to property format
                        property_data = {
                            'Address': row.get('Address', ''),
                            'Beds': row.get('Beds', 'N/A'),
                            'Baths': row.get('Baths', 'N/A'),
                            'Sqft': row.get('Sqft', 'N/A'),
                            'Price': row.get('Price', 'N/A'),
                            'Agent_Name': row.get('Agent_Name', 'N/A'),
                            'Agent_Phone': row.get('Agent_Phone', 'N/A'),
                            'URL': row.get('URL', '')
                        }

                        # Skip if essential data missing
                        if not property_data['Address'] or property_data['Address'] == 'N/A':
                            continue

                        # Add to Pipedrive Lead In stage
                        if self.add_to_pipedrive(property_data):
                            # Mark as processed
                            self.processed_csv_properties.add(property_id)
                            new_properties_count += 1

                            # Add to SMS queue if agent info available and not messaged
                            if (property_data.get('Agent_Phone') and
                                property_data.get('Agent_Phone') != 'N/A' and
                                property_data.get('Agent_Phone') not in self.messaged_agents):
                                self.sms_queue.put(property_data)

                            # Rate limiting - pause after every 5 properties
                            if new_properties_count % 5 == 0:
                                time.sleep(3)

                    except Exception as e:
                        logging.error(f"Error processing CSV row: {str(e)}")
                        continue

                if new_properties_count > 0:
                    logging.info(f"📊 Added {new_properties_count} new CSV properties to Pipedrive Lead In stage")

                return new_properties_count

        except Exception as e:
            logging.error(f"Error processing CSV for Pipedrive: {str(e)}")
            return 0

    def response_monitor_worker(self):
        """Enhanced response monitor with automatic tracking"""
        logging.info("👂 Starting enhanced response monitor worker")

        from enhanced_response_tracker import EnhancedResponseTracker

        # Initialize enhanced response tracker
        response_tracker = EnhancedResponseTracker()

        # Track sent messages from this session
        session_sent_messages = {}

        while self.running:
            try:
                # Update tracker with newly sent messages from this session
                for phone, message_info in session_sent_messages.items():
                    if phone not in response_tracker.sent_messages:
                        response_tracker.sent_messages[phone] = message_info

                # Run response tracking cycle
                processed_count = response_tracker.run_response_tracking_cycle()

                if processed_count > 0:
                    logging.info(f"📱 Processed {processed_count} agent responses")

                # Wait before next check
                time.sleep(45)  # Check every 45 seconds

            except Exception as e:
                logging.error(f"Error in enhanced response monitor: {str(e)}")
                time.sleep(60)

    def track_sent_message(self, prop, phone, message_id=None):
        """Track sent message for response monitoring"""
        try:
            # Find deal ID for this property
            deal_id = None

            # Search for deal by address
            import urllib.parse
            search_url = f"https://api.pipedrive.com/v1/deals/search?term={urllib.parse.quote(prop.get('Address', ''))}&api_token={PIPEDRIVE_TOKEN}"
            response = requests.get(search_url)

            if response.status_code == 200:
                data = response.json()
                items = data.get('data', {}).get('items', [])
                if items:
                    deal_id = items[0].get('item', {}).get('id')

            if deal_id:
                # Store sent message info for tracking
                sent_info = {
                    'deal_id': deal_id,
                    'address': prop.get('Address', ''),
                    'agent_name': prop.get('Agent_Name', ''),
                    'sent_time': datetime.now().isoformat(),
                    'message_id': message_id,
                    'status': 'sent'
                }

                # Add to session tracking (will be picked up by response monitor)
                if hasattr(self, 'session_sent_messages'):
                    self.session_sent_messages[phone] = sent_info

                logging.info(f"📋 Tracking sent message to {phone} for deal {deal_id}")

        except Exception as e:
            logging.error(f"Error tracking sent message: {str(e)}")

    def start_system(self):
        """Start all worker threads"""
        logging.info("🚀 Starting Integrated Zillow System")

        # Log multi-device status
        device_stats = self.device_manager.get_usage_stats()
        logging.info(f"📱 Multi-Device Status: {device_stats['enabled_devices']}/{device_stats['total_devices']} devices enabled")
        for device_key, stats in device_stats['device_stats'].items():
            if stats['enabled']:
                logging.info(f"   📞 {device_key}: {stats['phone_number']} ({stats['device_name']})")

        # Create and start threads
        threads = [
            threading.Thread(target=self.scraping_worker, name="Scraper"),
            threading.Thread(target=self.property_processor_worker, name="Processor"),
            threading.Thread(target=self.csv_monitor_worker, name="CSVMonitor"),
            threading.Thread(target=self.sms_worker, name="SMS"),
            threading.Thread(target=self.response_monitor_worker, name="ResponseMonitor")
        ]

        for thread in threads:
            thread.daemon = True
            thread.start()
            logging.info(f"✅ Started {thread.name} thread")

        try:
            # Keep main thread alive
            while self.running:
                time.sleep(10)

                # Print status
                logging.info(f"📊 Status - Properties Queue: {self.property_queue.qsize()}, SMS Queue: {self.sms_queue.qsize()}")

        except KeyboardInterrupt:
            logging.info("🛑 Stopping system...")
            self.running = False

            # Wait for threads to finish
            for thread in threads:
                thread.join(timeout=5)

            logging.info("✅ System stopped")

def main():
    """Main function"""
    print("🎯 Integrated Zillow Scraping & CRM System")
    print("=" * 60)
    print("Features:")
    print("✅ Continuous scraping with duplicate checking")
    print("✅ Infinite scroll for maximum properties")
    print("✅ Pipedrive integration (On Market stage)")
    print("✅ SMS gateway with agent messaging")
    print("✅ Response tracking and auto-response")
    print("✅ Multi-threading for optimal performance")
    print("=" * 60)

    confirm = input("🚀 Start the integrated system? (y/n): ")
    if confirm.lower() == 'y':
        system = ZillowIntegratedSystem()
        system.start_system()
    else:
        print("👋 Exiting...")

if __name__ == "__main__":
    main()
