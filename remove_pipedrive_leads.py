#!/usr/bin/env python3
"""
Remove Pipedrive Leads Tool
"""

import requests
import json
import time
from config import PIPEDRIVE_TOKEN, PIPEDRIVE_PIPELINE_ID as PIPELINE_ID

def get_all_deals():
    """Get all deals from Pipedrive"""
    print(f"\n📊 FETCHING ALL DEALS FROM PIPEDRIVE")
    print(f"="*50)
    
    all_deals = []
    start = 0
    limit = 100
    
    while True:
        try:
            url = f"https://api.pipedrive.com/v1/deals"
            params = {
                'api_token': PIPEDRIVE_TOKEN,
                'start': start,
                'limit': limit,
                'pipeline_id': PIPELINE_ID
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                deals = data.get('data', [])
                
                if not deals:
                    break
                
                all_deals.extend(deals)
                print(f"   Fetched {len(deals)} deals (Total: {len(all_deals)})")
                
                # Check if more data available
                additional_data = data.get('additional_data', {})
                pagination = additional_data.get('pagination', {})
                if not pagination.get('more_items_in_collection', False):
                    break
                
                start += limit
                time.sleep(0.5)  # Rate limiting
                
            else:
                print(f"❌ Error fetching deals: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            break
    
    print(f"\n📈 TOTAL DEALS FOUND: {len(all_deals)}")
    return all_deals

def display_deals(deals):
    """Display deals for selection"""
    print(f"\n📋 AVAILABLE DEALS:")
    print(f"="*80)
    
    for i, deal in enumerate(deals, 1):
        deal_id = deal.get('id')
        title = deal.get('title', 'No Title')
        stage_name = deal.get('stage_name', 'Unknown Stage')
        value = deal.get('value', 0)
        person_name = deal.get('person_name', 'No Contact')
        
        print(f"{i:3d}. ID: {deal_id} | {title[:40]:<40} | {stage_name:<15} | ${value:<8} | {person_name}")
    
    print(f"\n📊 Total: {len(deals)} deals")

def remove_deals(deal_ids):
    """Remove selected deals"""
    print(f"\n🗑️ REMOVING {len(deal_ids)} DEALS")
    print(f"="*50)
    
    removed_count = 0
    failed_count = 0
    
    for i, deal_id in enumerate(deal_ids, 1):
        try:
            print(f"   Removing {i}/{len(deal_ids)}: Deal ID {deal_id}...")
            
            url = f"https://api.pipedrive.com/v1/deals/{deal_id}"
            params = {'api_token': PIPEDRIVE_TOKEN}
            
            response = requests.delete(url, params=params)
            
            if response.status_code == 200:
                print(f"   ✅ Removed Deal ID {deal_id}")
                removed_count += 1
            else:
                print(f"   ❌ Failed to remove Deal ID {deal_id}: {response.status_code}")
                failed_count += 1
            
            # Rate limiting
            time.sleep(0.5)
            
        except Exception as e:
            print(f"   ❌ Error removing Deal ID {deal_id}: {str(e)}")
            failed_count += 1
    
    print(f"\n📊 REMOVAL RESULTS:")
    print(f"   Successfully Removed: {removed_count}")
    print(f"   Failed to Remove: {failed_count}")
    print(f"   Total Processed: {len(deal_ids)}")
    
    return removed_count

def remove_all_deals():
    """Remove all deals from pipeline"""
    print(f"\n⚠️ REMOVING ALL DEALS FROM PIPELINE")
    print(f"="*50)
    
    deals = get_all_deals()
    
    if not deals:
        print(f"✅ No deals found to remove")
        return True
    
    print(f"\n⚠️ WARNING: This will remove ALL {len(deals)} deals!")
    confirm = input(f"Type 'DELETE ALL' to confirm: ").strip()
    
    if confirm == 'DELETE ALL':
        deal_ids = [deal['id'] for deal in deals]
        removed_count = remove_deals(deal_ids)
        
        if removed_count == len(deals):
            print(f"\n🎯 ALL DEALS REMOVED SUCCESSFULLY!")
            return True
        else:
            print(f"\n⚠️ Some deals could not be removed")
            return False
    else:
        print(f"👋 Removal cancelled")
        return False

def remove_specific_deals():
    """Remove specific deals by selection"""
    print(f"\n🎯 REMOVING SPECIFIC DEALS")
    print(f"="*50)
    
    deals = get_all_deals()
    
    if not deals:
        print(f"✅ No deals found")
        return True
    
    display_deals(deals)
    
    print(f"\n📝 SELECT DEALS TO REMOVE:")
    print(f"   Enter deal numbers separated by commas (e.g., 1,3,5)")
    print(f"   Enter 'all' to remove all deals")
    print(f"   Enter 'cancel' to cancel")
    
    selection = input(f"\nSelection: ").strip().lower()
    
    if selection == 'cancel':
        print(f"👋 Removal cancelled")
        return False
    elif selection == 'all':
        return remove_all_deals()
    else:
        try:
            # Parse selection
            selected_numbers = [int(x.strip()) for x in selection.split(',')]
            selected_deals = []
            
            for num in selected_numbers:
                if 1 <= num <= len(deals):
                    selected_deals.append(deals[num-1])
                else:
                    print(f"⚠️ Invalid selection: {num}")
            
            if not selected_deals:
                print(f"❌ No valid deals selected")
                return False
            
            # Show selected deals
            print(f"\n📋 SELECTED DEALS FOR REMOVAL:")
            for deal in selected_deals:
                print(f"   ID: {deal['id']} | {deal.get('title', 'No Title')}")
            
            confirm = input(f"\n⚠️ Remove {len(selected_deals)} deals? (y/n): ").strip().lower()
            
            if confirm == 'y':
                deal_ids = [deal['id'] for deal in selected_deals]
                removed_count = remove_deals(deal_ids)
                
                if removed_count == len(selected_deals):
                    print(f"\n🎯 SELECTED DEALS REMOVED SUCCESSFULLY!")
                    return True
                else:
                    print(f"\n⚠️ Some deals could not be removed")
                    return False
            else:
                print(f"👋 Removal cancelled")
                return False
                
        except ValueError:
            print(f"❌ Invalid input format")
            return False

def remove_recent_deals():
    """Remove only recent deals (last 10)"""
    print(f"\n🕒 REMOVING RECENT DEALS")
    print(f"="*50)
    
    deals = get_all_deals()
    
    if not deals:
        print(f"✅ No deals found")
        return True
    
    # Sort by ID (recent deals have higher IDs)
    deals.sort(key=lambda x: x['id'], reverse=True)
    recent_deals = deals[:10]  # Last 10 deals
    
    print(f"\n📋 RECENT DEALS (Last 10):")
    for i, deal in enumerate(recent_deals, 1):
        print(f"{i:2d}. ID: {deal['id']} | {deal.get('title', 'No Title')[:40]}")
    
    confirm = input(f"\n⚠️ Remove these {len(recent_deals)} recent deals? (y/n): ").strip().lower()
    
    if confirm == 'y':
        deal_ids = [deal['id'] for deal in recent_deals]
        removed_count = remove_deals(deal_ids)
        
        if removed_count == len(recent_deals):
            print(f"\n🎯 RECENT DEALS REMOVED SUCCESSFULLY!")
            return True
        else:
            print(f"\n⚠️ Some deals could not be removed")
            return False
    else:
        print(f"👋 Removal cancelled")
        return False

def main():
    """Main function"""
    print(f"\n🗑️ PIPEDRIVE LEADS REMOVAL TOOL")
    print(f"Remove deals from Pipedrive pipeline")
    
    print(f"\n📋 REMOVAL OPTIONS:")
    print(f"1. Remove specific deals (select by number)")
    print(f"2. Remove recent deals (last 10)")
    print(f"3. Remove ALL deals (⚠️ DANGEROUS)")
    print(f"4. Just view deals (no removal)")
    print(f"5. Cancel")
    
    choice = input(f"\nSelect option (1-5): ").strip()
    
    if choice == '1':
        success = remove_specific_deals()
    elif choice == '2':
        success = remove_recent_deals()
    elif choice == '3':
        success = remove_all_deals()
    elif choice == '4':
        deals = get_all_deals()
        if deals:
            display_deals(deals)
        success = True
    elif choice == '5':
        print(f"👋 Cancelled")
        success = True
    else:
        print(f"❌ Invalid option")
        success = False
    
    if success:
        print(f"\n✅ Operation completed successfully!")
    else:
        print(f"\n⚠️ Operation completed with issues")

if __name__ == "__main__":
    main()
