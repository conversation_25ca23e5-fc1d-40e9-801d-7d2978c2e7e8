#!/usr/bin/env python3
"""
Test Client Device Number - Send SMS to check actual device number
"""

import requests
import json

def send_test_sms_to_check_device():
    """Send test SMS to your number to check what device number appears"""
    print(f"\n📱 SENDING TEST SMS TO CHECK CLIENT DEVICE NUMBER")
    print(f"="*70)
    
    # Your number (to receive test SMS)
    your_number = "+13214304820"
    
    # SMS Gateway credentials
    sms_api_url = 'https://humilix.com/sms/services/send.php'
    sms_token = '619bfcdb57e507d6fa1c96f836db7215b454af9d'
    
    # Test message
    test_message = "TEST: This is a test message to check what number appears as sender. Please check what number you receive this from."
    
    print(f"📞 Sending test SMS:")
    print(f"   To: {your_number}")
    print(f"   Message: {test_message}")
    print(f"   Gateway: Humilix SMS")
    
    try:
        # SMS API parameters - Fixed parameter types
        sms_params = {
            'key': sms_token,
            'number': your_number,
            'message': test_message,
            'devices': '',  # Let system choose device
            'type': 'sms',
            'useRandomDevice': 0,  # Changed to int
            'prioritize': 1  # Changed to int
        }
        
        print(f"\n🔄 Sending SMS...")
        
        response = requests.get(sms_api_url, params=sms_params, timeout=30)
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ SMS API Response:")
                print(f"   Status Code: {response.status_code}")
                print(f"   Response: {json.dumps(result, indent=2)}")
                
                # Check for success indicators
                if result.get('success') or result.get('status') == 'success':
                    print(f"\n🎉 TEST SMS SENT SUCCESSFULLY!")
                    print(f"📱 Please check your phone ({your_number}) for the test message")
                    print(f"📋 Note down what number appears as the sender")
                    print(f"📋 That will be your client's actual device number")
                    
                    # Try to extract device info from response
                    if 'data' in result:
                        data = result['data']
                        if 'messages' in data and len(data['messages']) > 0:
                            message_info = data['messages'][0]
                            device_id = message_info.get('deviceID', 'Unknown')
                            message_id = message_info.get('ID', 'Unknown')
                            
                            print(f"\n📊 SMS Details:")
                            print(f"   Message ID: {message_id}")
                            print(f"   Device ID: {device_id}")
                            
                    return True
                else:
                    print(f"⚠️ SMS may not have been sent successfully")
                    print(f"   Response: {result}")
                    return False
                    
            except json.JSONDecodeError:
                print(f"✅ SMS Response (not JSON):")
                print(f"   Status Code: {response.status_code}")
                print(f"   Content: {response.text[:300]}...")
                
                # Sometimes non-JSON response can still mean success
                if "success" in response.text.lower() or response.status_code == 200:
                    print(f"\n🎉 TEST SMS LIKELY SENT!")
                    print(f"📱 Please check your phone ({your_number}) for the test message")
                    return True
                else:
                    return False
        else:
            print(f"❌ SMS API Error:")
            print(f"   Status Code: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error sending test SMS: {str(e)}")
        return False

def check_available_devices():
    """Try to check what devices are available"""
    print(f"\n🔍 CHECKING AVAILABLE DEVICES")
    print(f"="*50)
    
    sms_token = '619bfcdb57e507d6fa1c96f836db7215b454af9d'
    
    # Try different device check endpoints
    device_endpoints = [
        f'https://humilix.com/sms/api.php?token={sms_token}&action=devices',
        f'https://humilix.com/sms/api.php?token={sms_token}&action=get_devices',
        f'https://humilix.com/sms/services/send.php?key={sms_token}&action=devices'
    ]
    
    for endpoint in device_endpoints:
        try:
            print(f"\n🔍 Trying: {endpoint[:60]}...")
            response = requests.get(endpoint, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON Response: {json.dumps(data, indent=2)[:200]}...")
                except:
                    if "device" in response.text.lower():
                        print(f"✅ Text Response (contains 'device'): {response.text[:200]}...")
                    else:
                        print(f"⚠️ Text Response: {response.text[:100]}...")
            else:
                print(f"❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def main():
    """Main function"""
    print(f"\n📱 CLIENT DEVICE NUMBER CHECKER")
    print(f"Send test SMS to check actual client device number")
    
    print(f"\n⚠️ IMPORTANT:")
    print(f"   Your number: +13214304820")
    print(f"   Need to find: Client's actual device number in SMS gateway")
    print(f"   Method: Send test SMS and check sender number")
    
    choice = input(f"\n🚀 Send test SMS to check device number? (y/n): ").strip().lower()
    
    if choice == 'y':
        # Check available devices first
        check_available_devices()
        
        # Send test SMS
        success = send_test_sms_to_check_device()
        
        if success:
            print(f"\n📱 NEXT STEPS:")
            print(f"1. Check your phone (+13214304820) for test message")
            print(f"2. Note what number appears as sender")
            print(f"3. That number is your client's device number")
            print(f"4. Update system config with that number")
            
            print(f"\n🔧 AFTER GETTING THE NUMBER:")
            print(f"   Tell me: 'Client device number is +1XXXXXXXXXX'")
            print(f"   I will update all config files with correct number")
            
        else:
            print(f"\n⚠️ Test SMS may have failed")
            print(f"   Try checking SMS gateway manually")
            print(f"   Or check device configuration")
            
        print(f"\n📋 ALTERNATIVE METHOD:")
        print(f"   1. Login to https://humilix.com/sms/")
        print(f"   2. Go to Devices section")
        print(f"   3. Check what phone number is registered")
        print(f"   4. That's your client's device number")
        
    else:
        print(f"👋 Test cancelled")
        print(f"\n💡 You can also:")
        print(f"   1. Check SMS gateway dashboard manually")
        print(f"   2. Look at device settings")
        print(f"   3. Check what number is registered for client")

if __name__ == "__main__":
    main()
