#!/usr/bin/env python3
"""
Device Manager for SMS System
Manages SMS devices and their configurations
"""

import json
import logging
from datetime import datetime

class DeviceManager:
    """Simple device manager for SMS system"""
    
    def __init__(self, config_file='multi_device_config.json'):
        self.config_file = config_file
        self.devices = []
        self.current_device_index = 0
        self.load_config()
        
        logging.info(f"📱 Device Manager initialized with {len(self.devices)} devices")
    
    def load_config(self):
        """Load device configuration"""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            self.devices = [d for d in config.get('devices', []) if d.get('enabled', True)]
            
            if not self.devices:
                # Fallback device
                self.devices = [{
                    'device_id': '10',
                    'device_name': 'Galaxy Note9',
                    'sim_slot': '0',
                    'phone_number': '+19542547716',
                    'config_string': '10|0',
                    'priority': 1,
                    'enabled': True,
                    'max_messages_per_hour': 100
                }]
            
            logging.info(f"✅ Loaded {len(self.devices)} devices from config")
            
        except Exception as e:
            logging.error(f"❌ Error loading device config: {str(e)}")
            # Fallback to default device
            self.devices = [{
                'device_id': '10',
                'device_name': 'Galaxy Note9',
                'sim_slot': '0',
                'phone_number': '+19542547716',
                'config_string': '10|0',
                'priority': 1,
                'enabled': True,
                'max_messages_per_hour': 100
            }]
    
    def get_primary_device(self):
        """Get the primary device for SMS sending"""
        if self.devices:
            return self.devices[0]
        return None
    
    def get_device_config_string(self):
        """Get device configuration string for SMS API"""
        device = self.get_primary_device()
        if device:
            return device.get('config_string', '10|0')
        return '10|0'
    
    def get_device_phone_number(self):
        """Get device phone number"""
        device = self.get_primary_device()
        if device:
            return device.get('phone_number', '+19542547716')
        return '+19542547716'
    
    def get_device_info(self):
        """Get device information"""
        device = self.get_primary_device()
        if device:
            return {
                'device_id': device.get('device_id', '10'),
                'device_name': device.get('device_name', 'Galaxy Note9'),
                'phone_number': device.get('phone_number', '+19542547716'),
                'config_string': device.get('config_string', '10|0'),
                'enabled': device.get('enabled', True)
            }
        return {
            'device_id': '10',
            'device_name': 'Galaxy Note9',
            'phone_number': '+19542547716',
            'config_string': '10|0',
            'enabled': True
        }
    
    def is_device_available(self):
        """Check if device is available for SMS"""
        device = self.get_primary_device()
        return device is not None and device.get('enabled', False)
    
    def get_all_devices(self):
        """Get all devices"""
        return self.devices
    
    def get_device_count(self):
        """Get number of available devices"""
        return len(self.devices)
    
    def get_enabled_device_count(self):
        """Get number of enabled devices"""
        return len([d for d in self.devices if d.get('enabled', False)])

# Test function
def test_device_manager():
    """Test the device manager"""
    manager = DeviceManager()
    
    print("Testing device manager...")
    
    print(f"Device count: {manager.get_device_count()}")
    print(f"Enabled devices: {manager.get_enabled_device_count()}")
    print(f"Primary device: {manager.get_primary_device()}")
    print(f"Device info: {manager.get_device_info()}")
    print(f"Config string: {manager.get_device_config_string()}")
    print(f"Phone number: {manager.get_device_phone_number()}")
    print(f"Available: {manager.is_device_available()}")

if __name__ == "__main__":
    test_device_manager()
