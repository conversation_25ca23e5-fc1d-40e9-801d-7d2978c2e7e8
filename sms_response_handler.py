#!/usr/bin/env python3
"""
SMS Response Handler for Zillow System
Handles incoming SMS responses and auto-replies
"""

import requests
import json
import time
import logging
from datetime import datetime
import urllib.parse

class SMSResponseHandler:
    def __init__(self, pipedrive_token, sms_token):
        self.pipedrive_token = pipedrive_token
        self.sms_token = sms_token
        self.auto_response = "Thank you for your response! We'll get back to you shortly with more details."
        self.sms_api_url = "https://humilix.com/sms/services/send.php"
        self.working_device_config = "10|0"  # CLIENT DEVICE - Galaxy Note9 SIM 0
        
    def check_incoming_messages(self):
        """Check for incoming SMS messages using Humilix API"""
        try:
            # Enhanced method to check for incoming messages
            logging.info("🔍 Checking for incoming SMS messages...")

            # Method 1: Check with services endpoint
            incoming_url = f"https://humilix.com/sms/services/receive.php"
            params = {
                'key': self.sms_token,
                'last': '50'  # Get last 50 messages
            }

            response = requests.get(incoming_url, params=params, timeout=15)

            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        messages = data.get('data', [])
                        logging.info(f"📱 Found {len(messages)} incoming messages")
                        return messages
                except json.JSONDecodeError:
                    logging.warning("⚠️ Non-JSON response from SMS API")

            # Method 2: Try with main API endpoint
            alt_url = f"https://humilix.com/sms/api.php"
            alt_params = {
                'key': self.sms_token,
                'action': 'get_messages',
                'type': 'received',
                'limit': '50'
            }

            response2 = requests.get(incoming_url, params=alt_params, timeout=10)

            if response2.status_code == 200:
                try:
                    data = response2.json()
                    if isinstance(data, dict):
                        return data.get('messages', data.get('inbox', []))
                    elif isinstance(data, list):
                        return data
                except:
                    pass

            # Method 3: Try services endpoint
            services_url = "https://humilix.com/sms/services/receive.php"
            services_params = {
                'key': self.sms_token
            }

            response3 = requests.get(services_url, params=services_params, timeout=10)

            if response3.status_code == 200:
                try:
                    data = response3.json()
                    if isinstance(data, dict):
                        return data.get('messages', [])
                    elif isinstance(data, list):
                        return data
                except:
                    pass

            logging.warning("No incoming messages endpoint responded successfully")

        except Exception as e:
            logging.error(f"Error checking incoming messages: {str(e)}")

        return []
    
    def process_incoming_message(self, message):
        """Process an incoming SMS message"""
        try:
            sender_phone = message.get('from', '')
            message_text = message.get('text', '')
            received_time = message.get('timestamp', datetime.now().isoformat())
            
            logging.info(f"📱 Incoming SMS from {sender_phone}: {message_text}")
            
            # Find the property/deal associated with this phone number
            deal_info = self.find_deal_by_phone(sender_phone)
            
            if deal_info:
                # Auto-response disabled per client request
                # self.send_auto_response(sender_phone, message_text)
                
                # Update Pipedrive deal to "Responded" stage
                self.update_deal_to_responded(deal_info['deal_id'], message_text, received_time)
                
                # Add response note
                self.add_response_note(deal_info['deal_id'], sender_phone, message_text, received_time)
                
                logging.info(f"✅ Processed response for deal {deal_info['deal_id']}")
            else:
                logging.warning(f"⚠️ No deal found for phone {sender_phone}")
                
        except Exception as e:
            logging.error(f"Error processing incoming message: {str(e)}")
    
    def find_deal_by_phone(self, phone):
        """Find Pipedrive deal by agent phone number"""
        try:
            # Search deals by phone number in notes
            search_url = f"https://api.pipedrive.com/v1/deals/search?term={urllib.parse.quote(phone)}&api_token={self.pipedrive_token}"
            
            response = requests.get(search_url)
            
            if response.status_code == 200:
                search_data = response.json()
                deals = search_data.get('data', {}).get('items', [])
                
                if deals:
                    deal = deals[0].get('item', {})
                    return {
                        'deal_id': deal.get('id'),
                        'title': deal.get('title', ''),
                        'stage_id': deal.get('stage_id')
                    }
            
        except Exception as e:
            logging.error(f"Error finding deal by phone: {str(e)}")
        
        return None
    

    
    def update_deal_to_responded(self, deal_id, message_text, received_time):
        """Update deal stage to 'Responded'"""
        try:
            # Update deal stage (assuming stage 2 is "Responded")
            update_data = {
                'stage_id': 2  # Adjust this to your actual "Responded" stage ID
            }
            
            update_url = f"https://api.pipedrive.com/v1/deals/{deal_id}?api_token={self.pipedrive_token}"
            response = requests.put(update_url, json=update_data)
            
            if response.status_code == 200:
                logging.info(f"✅ Deal {deal_id} moved to Responded stage")
            else:
                logging.error(f"❌ Failed to update deal stage: {response.status_code}")
                
        except Exception as e:
            logging.error(f"Error updating deal stage: {str(e)}")
    
    def add_response_note(self, deal_id, phone, message_text, received_time):
        """Add response note to Pipedrive deal"""
        try:
            note_content = f"""
📱 AGENT RESPONSE RECEIVED
📞 From: {phone}
⏰ Time: {received_time}

💬 Message:
{message_text}

🤖 Auto-response sent: "{self.auto_response}"
            """.strip()
            
            note_data = {
                'content': note_content,
                'deal_id': deal_id
            }
            
            note_url = f"https://api.pipedrive.com/v1/notes?api_token={self.pipedrive_token}"
            response = requests.post(note_url, json=note_data)
            
            if response.status_code == 201:
                logging.info(f"✅ Added response note to deal {deal_id}")
            else:
                logging.error(f"❌ Failed to add response note: {response.status_code}")
                
        except Exception as e:
            logging.error(f"Error adding response note: {str(e)}")

    def send_auto_response(self, target_phone, original_message=""):
        """Send automatic response using confirmed working configuration"""
        try:
            logging.info(f"📤 Sending auto-response to {target_phone}")

            # Create personalized auto-response
            if original_message:
                auto_message = f"Thank you for your response! Your message '{original_message}' was received. This is an automated reply. We will contact you shortly with more details."
            else:
                auto_message = self.auto_response

            # Use confirmed working SMS configuration
            sms_data = {
                'key': self.sms_token,
                'number': target_phone,
                'message': auto_message,
                'devices': self.working_device_config,  # 9|1 - CONFIRMED WORKING
                'type': 'sms',
                'prioritize': '0'
            }

            logging.info(f"💬 Auto-response: {auto_message}")
            logging.info(f"🔧 Using device config: {self.working_device_config}")

            # Send auto-response
            response = requests.post(self.sms_api_url, data=sms_data, timeout=30)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        msg_info = result.get('data', {}).get('messages', [{}])[0]
                        response_id = msg_info.get('ID')
                        device_id = msg_info.get('deviceID')
                        sim_slot = msg_info.get('simSlot')

                        logging.info(f"✅ Auto-response sent successfully!")
                        logging.info(f"   Response ID: {response_id}")
                        logging.info(f"   Device ID: {device_id}")
                        logging.info(f"   SIM Slot: {sim_slot}")

                        return response_id
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        logging.error(f"❌ Auto-response failed: {error_msg}")
                        return None
                except json.JSONDecodeError:
                    logging.warning(f"⚠️ Auto-response sent (non-JSON response)")
                    return True
            else:
                logging.error(f"❌ Auto-response HTTP error: {response.status_code}")
                return None

        except Exception as e:
            logging.error(f"❌ Error sending auto-response: {str(e)}")
            return None

    def monitor_responses(self):
        """Continuously monitor for SMS responses"""
        logging.info("👂 Starting SMS response monitoring")
        
        processed_messages = set()
        
        while True:
            try:
                # Check for incoming messages
                incoming_messages = self.check_incoming_messages()
                
                for message in incoming_messages:
                    message_id = message.get('id', '')
                    
                    # Skip if already processed
                    if message_id and message_id not in processed_messages:
                        self.process_incoming_message(message)
                        processed_messages.add(message_id)
                
                # Wait before next check
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                logging.info("🛑 Stopping response monitoring")
                break
            except Exception as e:
                logging.error(f"Error in response monitoring: {str(e)}")
                time.sleep(60)  # Wait longer on error

def test_response_handler():
    """Test the response handler with sample data"""
    handler = SMSResponseHandler(
        pipedrive_token='****************************************',
        sms_token='619bfcdb57e507d6fa1c96f836db7215b454af9d'
    )
    
    # Test with sample message
    sample_message = {
        'id': 'test_123',
        'from': '+923214304820',  # Your Pakistan test phone
        'text': 'Yes, the property is still available. When would you like to schedule a viewing?',
        'timestamp': datetime.now().isoformat()
    }
    
    print("🧪 Testing response handler...")
    handler.process_incoming_message(sample_message)

if __name__ == "__main__":
    # For testing
    test_response_handler()
