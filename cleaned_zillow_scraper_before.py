#!/usr/bin/env python3
"""
Enhanced version of your working scraper with JSON extraction for more properties
"""

import requests
import csv
import json
import time
import re
import urllib.parse
from bs4 import BeautifulSoup

# Configuration
SCRAPER_API_KEY = '91b405af27e201a62e346fefbdca9664'
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
}

def build_zillow_url(page_number):
    search_query_state = {
        "pagination": {"currentPage": page_number},
        "usersSearchTerm": "Florida",
        "mapBounds": {
            "west": -96.44986467187502,
            "east": -71.15933732812502,
            "south": 20.393022319423494,
            "north": 34.639525177290345
        },
        "mapZoom": 6,
        "filterState": {
            "price": {"min": 100000, "max": 400000},
            "mp": {"min": 504, "max": 2015},
            "beds": {"min": 2},
            "baths": {"min": 2},
            "gar": {"value": True},
            "sqft": {"min": 1000},
            "lot": {"min": 0, "max": 43560},
            "built": {"min": 1970},
            "55plus": {"value": "e"},
            "att": {"value": "AS-IS"}
        },
        "isListVisible": True,
        "category": "cat1",
        "regionSelection": [{"regionId": 14, "regionType": 2}]
    }

    encoded_state = urllib.parse.quote(json.dumps(search_query_state))
    base_url = f"https://www.zillow.com/fl/{page_number}_p/?searchQueryState={encoded_state}"
    scraper_url = f"https://api.scraperapi.com/?api_key={SCRAPER_API_KEY}&url={urllib.parse.quote(base_url)}"
    
    return scraper_url

def load_existing_urls_from_csv():
    existing_urls = set()
    try:
        with open('zillow_florida_data.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # Skip header
            for row in reader:
                if len(row) > 7:  # URL column index (8th column = index 7) - FIXED
                    url = row[7].strip()  # URL column is at index 7 - FIXED
                    if url and url != 'N/A':  # Only add valid URLs
                        existing_urls.add(url)
        print(f"📋 Loaded {len(existing_urls)} existing properties from CSV")
        if existing_urls:
            print(f"🔍 First few existing URLs: {list(existing_urls)[:3]}")
    except FileNotFoundError:
        print("📄 No existing CSV file found, starting fresh")
        with open('zillow_florida_data.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Address', 'Beds', 'Baths', 'Sqft', 'Price', 'Agent_Name', 'Agent_Phone', 'URL'])
    return existing_urls

def extract_properties_from_json(html_content):
    """ENHANCED: Extract more properties from JSON data"""
    properties = []
    
    try:
        print("🔍 Extracting properties from JSON data...")
        
        zpid_pattern = r'"zpid":"([^"]+)"'
        zpid_matches = re.finditer(zpid_pattern, html_content)
        
        zpid_contexts = []
        for match in zpid_matches:
            zpid = match.group(1)
            start = max(0, match.start() - 500)
            end = min(len(html_content), match.end() + 500)
            context = html_content[start:end]
            zpid_contexts.append((zpid, context))
        
        print(f"🆔 Found {len(zpid_contexts)} ZPID contexts in JSON")
        
        property_data = []
        for zpid, context in zpid_contexts:
            prop_data = {'zpid': zpid}
            
            patterns = {
                'address': r'"address":"([^"]+)"',
                'price': r'"price":"([^"]+)"',
                'detailUrl': r'"detailUrl":"([^"]+)"'
            }
            
            for field, pattern in patterns.items():
                match = re.search(pattern, context)
                if match:
                    value = match.group(1).strip('"')
                    prop_data[field] = value
            
            if 'address' in prop_data:
                property_data.append(prop_data)
        
        # Remove duplicates based on ZPID
        unique_properties = {}
        for prop in property_data:
            zpid = prop.get('zpid')
            if zpid and (zpid not in unique_properties or len(prop) > len(unique_properties[zpid])):
                unique_properties[zpid] = prop
        
        # Convert to expected format
        for prop_data in unique_properties.values():
            prop = {
                'address': prop_data.get('address', 'N/A'),
                'price': prop_data.get('price', 'N/A'),
                'beds': 'N/A',
                'baths': 'N/A',
                'area': 'N/A',
                'detailUrl': prop_data.get('detailUrl', 'N/A')
            }
            properties.append(prop)
        
        print(f"✅ Successfully extracted {len(properties)} unique properties from JSON")
        
    except Exception as e:
        print(f"❌ Error extracting from JSON: {str(e)}")
    
    return properties

def fetch_properties(page):
    """ENHANCED: Your original function with JSON extraction added"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            url = build_zillow_url(page)
            print(f"🔍 Fetching page {page} (Attempt {attempt + 1}/{max_retries})...")
            print(f"🌐 URL: {url}")

            # Your original timeout and retry logic (UNCHANGED)
            response = requests.get(url, headers=HEADERS, timeout=90)
            print(f"📊 Response Status: {response.status_code}")

            response.raise_for_status()
            break  # Success, exit retry loop

        except requests.exceptions.Timeout:
            print(f"⏰ Timeout on attempt {attempt + 1}")
            if attempt < max_retries - 1:
                print(f"🔄 Retrying in 5 seconds...")
                time.sleep(5)
                continue
            else:
                print(f"❌ All {max_retries} attempts failed due to timeout")
                return []
        except Exception as e:
            print(f"❌ Error on attempt {attempt + 1}: {str(e)}")
            if attempt < max_retries - 1:
                print(f"🔄 Retrying in 5 seconds...")
                time.sleep(5)
                continue
            else:
                print(f"❌ All {max_retries} attempts failed")
                return []

    try:
        # Parse HTML to extract properties (ORIGINAL WORKING METHOD)
        soup = BeautifulSoup(response.text, 'html.parser')
        property_cards = soup.find_all("article", {"data-test": "property-card"})
        print(f"✅ Found {len(property_cards)} property cards")

        properties = []

        # Your original HTML parsing logic (RESTORED)
        for card in property_cards:
                try:
                    prop = {}

                    # Extract address
                    address_element = card.find("address")
                    if not address_element:
                        address_element = card.select_one('a[data-test="property-card-link"] address')
                    if address_element:
                        prop['address'] = address_element.get_text(strip=True)
                    else:
                        prop['address'] = 'N/A'

                    # Extract price
                    price_element = card.find("span", {"data-test": "property-card-price"})
                    if price_element:
                        prop['price'] = price_element.get_text(strip=True)
                    else:
                        prop['price'] = 'N/A'

                    # Extract beds, baths, sqft
                    prop['beds'] = 'N/A'
                    prop['baths'] = 'N/A'
                    prop['area'] = 'N/A'

                    # Your original detail extraction logic (UNCHANGED)
                    detail_selectors = [
                        card.find_all("li"),
                        card.find_all("span"),
                        card.find_all("div", class_=lambda x: x and 'bed' in x.lower()),
                        card.find_all("div", class_=lambda x: x and 'bath' in x.lower()),
                        card.find_all("div", class_=lambda x: x and 'sqft' in x.lower())
                    ]

                    all_elements = []
                    for selector_result in detail_selectors:
                        all_elements.extend(selector_result)

                    for element in all_elements:
                        text = element.get_text().lower().strip()
                        if text:
                            # Extract beds
                            if ('bed' in text or 'bd' in text) and prop['beds'] == 'N/A':
                                if any(char.isdigit() for char in text):
                                    prop['beds'] = element.get_text(strip=True)

                            # Extract baths
                            elif ('bath' in text or 'ba' in text) and prop['baths'] == 'N/A':
                                if any(char.isdigit() for char in text):
                                    prop['baths'] = element.get_text(strip=True)

                            # Extract sqft
                            elif ('sqft' in text or 'sq ft' in text or 'ft²' in text) and prop['area'] == 'N/A':
                                if any(char.isdigit() for char in text):
                                    prop['area'] = element.get_text(strip=True)

                    # Extract URL
                    link_element = card.find("a", {"data-test": "property-card-link"})
                    if link_element and link_element.get('href'):
                        href = link_element['href'].split('?')[0]
                        if href.startswith('/homedetails'):
                            prop['detailUrl'] = href
                        elif href.startswith('https://www.zillow.com/homedetails'):
                            prop['detailUrl'] = href.replace('https://www.zillow.com', '')
                        else:
                            prop['detailUrl'] = href
                    else:
                        prop['detailUrl'] = 'N/A'

                    properties.append(prop)

                except Exception as e:
                    print(f"❌ Error parsing property card: {str(e)}")
                    continue

        print(f"✅ Successfully parsed {len(properties)} properties")

        # Debug: Show first property if available
        if properties:
            print(f"🔍 First property: {properties[0]}")

        return properties

    except Exception as e:
        print(f"❌ Error fetching page {page}: {str(e)}")
        print(f"❌ Response text (first 500 chars): {response.text[:500] if 'response' in locals() else 'No response'}")
        return []

# IMPROVED: Enhanced agent extraction with better patterns (UPDATED)
def get_agent_info(detail_url):
    try:
        if not detail_url or detail_url == 'N/A':
            return {'agent_name': 'N/A', 'agent_phone': 'N/A'}

        # Ensure proper URL construction
        if detail_url.startswith('https://'):
            zillow_url = detail_url
        elif detail_url.startswith('/'):
            zillow_url = f"https://www.zillow.com{detail_url}"
        else:
            zillow_url = f"https://www.zillow.com/{detail_url}"

        encoded_zillow_url = urllib.parse.quote(zillow_url, safe=':/?#[]@!$&\'()*+,;=')
        full_url = f"https://api.scraperapi.com/?api_key={SCRAPER_API_KEY}&url={encoded_zillow_url}"
        res = requests.get(full_url, headers=HEADERS, timeout=60)  # Increased timeout
        res.raise_for_status()

        agent = {
            'agent_name': 'N/A',
            'agent_phone': 'N/A'
        }

        # Parse HTML for better extraction
        soup = BeautifulSoup(res.content, 'html.parser')

        # Method 1: Look for agent attribution
        agent_selectors = [
            '[data-testid="attribution-LISTING_AGENT"]',
            '.agent-name',
            '.listing-agent-name'
        ]

        for selector in agent_selectors:
            agent_elem = soup.select_one(selector)
            if agent_elem:
                agent_text = agent_elem.get_text(strip=True)
                # Clean agent name and extract phone
                if agent_text:
                    # Extract phone number from agent text
                    phone_pattern = r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
                    phone_match = re.search(phone_pattern, agent_text)
                    if phone_match:
                        agent['agent_phone'] = phone_match.group()
                        # Remove phone from name and clean
                        agent_name = re.sub(phone_pattern, '', agent_text).strip().rstrip(',').strip()
                        # Remove extra numbers and clean
                        agent_name = re.sub(r'\d+', '', agent_name).strip().rstrip(',').strip()
                        # Remove extra commas and spaces
                        agent_name = re.sub(r'[,\s]+$', '', agent_name).strip()
                        if agent_name and len(agent_name) > 2:
                            agent['agent_name'] = agent_name
                    else:
                        # Clean agent name from numbers and extra characters
                        clean_name = re.sub(r'\d+', '', agent_text).strip().rstrip(',').strip()
                        clean_name = re.sub(r'[,\s]+$', '', clean_name).strip()
                        if clean_name and len(clean_name) > 2:
                            agent['agent_name'] = clean_name
                    break

        # Method 2: Contact buttons
        if agent['agent_name'] == 'N/A':
            contact_buttons = soup.find_all('button', string=re.compile(r'Contact.*', re.IGNORECASE))
            for button in contact_buttons:
                button_text = button.get_text(strip=True)
                if 'Contact' in button_text and len(button_text) > 8:
                    agent_name = button_text.replace('Contact', '').strip()
                    if agent_name and agent_name not in ['Agent', 'Listing Agent']:
                        agent['agent_name'] = agent_name
                        break

        # Method 3: Phone number search
        if agent['agent_phone'] == 'N/A':
            phone_pattern = r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
            phone_matches = re.findall(phone_pattern, res.text)
            if phone_matches:
                for phone in phone_matches:
                    if not any(x in phone for x in ['800', '888', '877', '866', '844']):
                        agent['agent_phone'] = phone
                        break

        # Method 4: Enhanced JSON patterns (including dynamic data)
        json_patterns = [
            # Standard patterns
            r'"listingAgent":(\{.*?\})',
            r'"agent":\s*\{[^}]*"name":\s*"([^"]+)"[^}]*"phoneNumber":\s*"([^"]+)"',
            r'"contactAgent"[^}]*"name":\s*"([^"]+)"[^}]*"phone":\s*"([^"]+)"',
            r'"listingAgentName":\s*"([^"]+)".*?"listingAgentPhone":\s*"([^"]+)"',

            # Dynamic contact patterns
            r'"contactInfo"[^}]*"name":\s*"([^"]+)"[^}]*"phone":\s*"([^"]+)"',
            r'"agentContact"[^}]*"displayName":\s*"([^"]+)"[^}]*"phoneNumber":\s*"([^"]+)"',
            r'"primaryAgent"[^}]*"name":\s*"([^"]+)"[^}]*"phone":\s*"([^"]+)"',

            # Contact button data
            r'"contactButtonData"[^}]*"agentName":\s*"([^"]+)"[^}]*"phoneNumber":\s*"([^"]+)"',
            r'"showContactForm"[^}]*"agentName":\s*"([^"]+)"[^}]*"phone":\s*"([^"]+)"'
        ]

        for i, pattern in enumerate(json_patterns, 1):
            match = re.search(pattern, res.text)
            if match:
                try:
                    if i == 1:  # listingAgent object
                        agent_json = json.loads(match.group(1))
                        agent['agent_name'] = agent_json.get('name', 'N/A')
                        agent['agent_phone'] = agent_json.get('phoneNumber', 'N/A')
                    else:  # Direct name/phone patterns
                        agent['agent_name'] = match.group(1)
                        if len(match.groups()) > 1:
                            agent['agent_phone'] = match.group(2)

                    if agent['agent_name'] != 'N/A' and len(agent['agent_name']) > 2:
                        print(f"   ✅ Found agent via JSON pattern {i}")
                        break
                except:
                    continue

        # Method 2: HTML parsing (proven to work)
        if agent['agent_name'] == 'N/A':
            soup = BeautifulSoup(res.text, 'html.parser')

            # Try seller attribution (this worked in our tests)
            container = soup.find('div', {'data-testid': 'seller-attribution'})
            if container:
                # Look for any attribution element (not just LISTING_AGENT)
                attribution_elements = container.find_all('p', {'data-testid': re.compile(r'attribution')})
                if not attribution_elements:
                    # Fallback: look for any p elements in seller attribution
                    attribution_elements = container.find_all('p')

                for agent_element in attribution_elements:
                    # Method A: Look for 2 spans (standard format)
                    spans = agent_element.find_all('span')
                    if len(spans) >= 2:
                        potential_name = spans[0].get_text(strip=True)
                        potential_phone = spans[1].get_text(strip=True).rstrip(',')

                        # Accept any name that's not empty and has reasonable length
                        if potential_name and len(potential_name) > 1 and len(potential_name) < 100:
                            agent['agent_name'] = potential_name
                            agent['agent_phone'] = potential_phone
                            break

                    # Method B: Look for button + span (Property Owner format)
                    elif len(spans) == 1:
                        button = agent_element.find('button')
                        if button and spans:
                            potential_name = button.get_text(strip=True)
                            potential_phone = spans[0].get_text(strip=True).rstrip(',')

                            if potential_name and len(potential_name) > 1 and len(potential_name) < 100:
                                agent['agent_name'] = potential_name
                                agent['agent_phone'] = potential_phone
                                break

        # Method 3: Look for phone numbers with nearby names (NEW - finds dynamic content)
        if agent['agent_name'] == 'N/A':
            # Find phone numbers and look for names nearby
            phone_pattern = r'(\(\d{3}\)\s*\d{3}-\d{4}|\d{3}[-.\s]\d{3}[-.\s]\d{4})'
            phone_matches = re.finditer(phone_pattern, res.text)

            for phone_match in phone_matches:
                phone = phone_match.group(0)
                start_pos = max(0, phone_match.start() - 200)
                end_pos = min(len(res.text), phone_match.end() + 200)
                context = res.text[start_pos:end_pos]

                # Look for names in the context (capitalized words)
                name_pattern = r'\b([A-Z][a-z]+\s+[A-Z][a-z]+)\b'
                name_matches = re.findall(name_pattern, context)

                for name in name_matches:
                    # Filter out common non-name words (but allow "listing provided by" context)
                    if not any(word in name.lower() for word in ['contact', 'agent', 'phone', 'call', 'email', 'zillow']):
                        if len(name) > 5 and len(name) < 50:
                            agent['agent_name'] = name.strip()
                            agent['agent_phone'] = phone
                            print(f"   ✅ Found agent via phone context: {name}")
                            break

                if agent['agent_name'] != 'N/A':
                    break

        # Method 3.5: Look for "Listing Provided by:" pattern specifically
        if agent['agent_name'] == 'N/A':
            listing_pattern = r'Listing\s+Provided\s+by[:\s]*([A-Za-z\s]+)\s+(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})'
            listing_match = re.search(listing_pattern, res.text, re.IGNORECASE)
            if listing_match:
                agent['agent_name'] = listing_match.group(1).strip()
                agent['agent_phone'] = listing_match.group(2).strip()
                print(f"   ✅ Found agent via 'Listing Provided by' pattern: {agent['agent_name']}")

        # Method 4: Alternative HTML selectors
        if agent['agent_name'] == 'N/A':
            soup = BeautifulSoup(res.text, 'html.parser')

            # Try other agent selectors
            agent_selectors = [
                'div[data-testid*="agent"]',
                'div[class*="agent"]',
                'span[class*="agent-name"]',
                'div[class*="contact"]'
            ]

            for selector in agent_selectors:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if text and len(text) > 5 and len(text) < 50:
                        # Look for phone numbers in the same element or nearby
                        phone_match = re.search(r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})', text)
                        if phone_match:
                            # Extract name (text before phone)
                            name_part = text.split(phone_match.group(0))[0].strip()
                            if len(name_part) > 2:
                                agent['agent_name'] = name_part
                                agent['agent_phone'] = phone_match.group(0)
                                break
                if agent['agent_name'] != 'N/A':
                    break

        # Clean phone number
        if agent['agent_phone'] != 'N/A':
            phone_clean = re.sub(r'\D+', '', agent['agent_phone'])[-10:]
            if len(phone_clean) == 10:
                agent['agent_phone'] = f"({phone_clean[:3]}) {phone_clean[3:6]}-{phone_clean[6:]}"

        # Debug output for troubleshooting
        if agent['agent_name'] == 'N/A':
            print(f"   ⚠️  No agent found - might be FSBO or private listing")

        return agent
    except Exception as e:
        print(f"❌ Error fetching agent info: {str(e)}")
        return {'agent_name': 'N/A', 'agent_phone': 'N/A'}

# Your original add_property_to_csv function (UNCHANGED)
def add_property_to_csv(address, beds, baths, sqft, price, agent_name, agent_phone, url):
    with open('zillow_florida_data.csv', 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([address, beds, baths, sqft, price, agent_name, agent_phone, url])

def get_property_details(detail_url):
    """Extract detailed property information from individual property page"""
    try:
        if not detail_url or detail_url == 'N/A':
            return {'beds': 'N/A', 'baths': 'N/A', 'sqft': 'N/A', 'price': 'N/A'}

        # Ensure proper URL construction
        if detail_url.startswith('https://'):
            zillow_url = detail_url
        elif detail_url.startswith('/'):
            zillow_url = f"https://www.zillow.com{detail_url}"
        else:
            zillow_url = f"https://www.zillow.com/{detail_url}"

        encoded_zillow_url = urllib.parse.quote(zillow_url, safe=':/?#[]@!$&\'()*+,;=')
        full_url = f"https://api.scraperapi.com/?api_key={SCRAPER_API_KEY}&url={encoded_zillow_url}"
        res = requests.get(full_url, headers=HEADERS, timeout=60)
        res.raise_for_status()

        soup = BeautifulSoup(res.content, 'html.parser')

        property_details = {
            'beds': 'N/A',
            'baths': 'N/A',
            'sqft': 'N/A',
            'price': 'N/A'
        }

        # Extract price
        price_selectors = [
            'span[data-testid="price"]',
            '.ds-price .ds-value',
            '.notranslate'
        ]

        for selector in price_selectors:
            price_elem = soup.select_one(selector)
            if price_elem and '$' in price_elem.get_text():
                property_details['price'] = price_elem.get_text(strip=True)
                break

        # Extract property facts - Method 1: Factsheet
        facts_section = soup.find('div', {'data-testid': 'bdp-factsheet'})
        if facts_section:
            facts = facts_section.find_all('span')
            for fact in facts:
                text = fact.get_text(strip=True)
                # Look for clean patterns in factsheet
                if re.match(r'^\d{1,2}bd$', text.lower()) and property_details['beds'] == 'N/A':
                    property_details['beds'] = text
                elif re.match(r'^\d{1,2}ba$', text.lower()) and property_details['baths'] == 'N/A':
                    property_details['baths'] = text
                elif re.match(r'^[\d,]{3,6}sqft$', text.lower()) and property_details['sqft'] == 'N/A':
                    property_details['sqft'] = text

        # Method 2: Search all spans for patterns
        if property_details['beds'] == 'N/A' or property_details['baths'] == 'N/A' or property_details['sqft'] == 'N/A':
            all_spans = soup.find_all('span')
            for span in all_spans:
                text = span.get_text(strip=True)
                # Look for exact patterns like "3bd", "2ba", "1,200sqft"
                if re.match(r'^\d{1,2}bd$', text.lower()) and property_details['beds'] == 'N/A':
                    property_details['beds'] = text
                elif re.match(r'^\d{1,2}ba$', text.lower()) and property_details['baths'] == 'N/A':
                    property_details['baths'] = text
                elif re.match(r'^[\d,]{3,6}sqft$', text.lower()) and property_details['sqft'] == 'N/A':
                    property_details['sqft'] = text

        # Method 2.5: Look for individual numbers next to "beds", "baths", "sqft" (ENHANCED)
        if property_details['beds'] == 'N/A' or property_details['baths'] == 'N/A' or property_details['sqft'] == 'N/A':

            # Look for beds
            if property_details['beds'] == 'N/A':
                bed_spans = soup.find_all('span', string=re.compile(r'^\d{1,2}$'))
                for span in bed_spans:
                    next_span = span.find_next_sibling('span')
                    if next_span and 'bed' in next_span.get_text().lower():
                        beds_num = span.get_text().strip()
                        if 1 <= int(beds_num) <= 20:
                            property_details['beds'] = f"{beds_num}bd"
                            break

            # Look for baths
            if property_details['baths'] == 'N/A':
                bath_spans = soup.find_all('span', string=re.compile(r'^\d{1,2}$'))
                for span in bath_spans:
                    next_span = span.find_next_sibling('span')
                    if next_span and 'bath' in next_span.get_text().lower():
                        baths_num = span.get_text().strip()
                        if 1 <= int(baths_num) <= 20:
                            property_details['baths'] = f"{baths_num}ba"
                            break

            # Look for sqft
            if property_details['sqft'] == 'N/A':
                sqft_spans = soup.find_all('span', string=re.compile(r'^[\d,]{3,6}$'))
                for span in sqft_spans:
                    next_span = span.find_next_sibling('span')
                    if next_span and 'sqft' in next_span.get_text().lower():
                        sqft_num = span.get_text().strip()
                        property_details['sqft'] = f"{sqft_num}sqft"
                        break

        # Method 3: Look in structured data
        if property_details['beds'] == 'N/A' or property_details['baths'] == 'N/A' or property_details['sqft'] == 'N/A':
            scripts = soup.find_all('script', type='application/ld+json')
            for script in scripts:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict):
                        if 'numberOfBedrooms' in data and property_details['beds'] == 'N/A':
                            property_details['beds'] = f"{data['numberOfBedrooms']}bd"
                        if 'numberOfBathroomsTotal' in data and property_details['baths'] == 'N/A':
                            property_details['baths'] = f"{data['numberOfBathroomsTotal']}ba"
                        if 'floorSize' in data and property_details['sqft'] == 'N/A':
                            property_details['sqft'] = f"{data['floorSize']}sqft"
                except:
                    continue

        return property_details

    except Exception as e:
        print(f"❌ Error extracting property details: {str(e)}")
        return {'beds': 'N/A', 'baths': 'N/A', 'sqft': 'N/A', 'price': 'N/A'}

# Your original main scraping function (UNCHANGED except for duplicate fix)
def scrape_zillow_complete():
    print("🚀 Starting Enhanced Zillow Florida Properties Scraper...")
    print("=" * 60)

    # Load existing URLs to avoid duplicates (FIXED)
    existing_urls = load_existing_urls_from_csv()

    page = 1
    total_new_properties = 0
    total_skipped = 0
    consecutive_empty_pages = 0

    while True:
        print(f"\n📄 Processing page {page}...")

        # Fetch properties from current page (ENHANCED)
        properties = fetch_properties(page)

        # If no properties found, check if we should stop
        if not properties:
            consecutive_empty_pages += 1
            print(f"❌ No properties found on page {page}")

            if consecutive_empty_pages >= 3:  # Stop after 3 consecutive empty pages
                print("🛑 Stopping: Found 3 consecutive empty pages")
                break
            else:
                page += 1
                continue
        else:
            consecutive_empty_pages = 0  # Reset counter

        print(f"✅ Found {len(properties)} properties on page {page}")

        page_new_count = 0
        page_skip_count = 0

        # Process each property
        for i, prop in enumerate(properties, 1):
            try:
                # Extract basic property data
                address = prop.get('address', 'N/A')
                beds = prop.get('beds', 'N/A')
                baths = prop.get('baths', 'N/A')
                sqft = prop.get('area', 'N/A')
                price = prop.get('price', 'N/A')
                detail_url = prop.get('detailUrl', 'N/A')

                # Create full URL for display and CSV
                if detail_url and detail_url != 'N/A':
                    if detail_url.startswith('https://'):
                        full_url = detail_url
                    elif detail_url.startswith('/'):
                        full_url = f"https://www.zillow.com{detail_url}"
                    else:
                        full_url = f"https://www.zillow.com/{detail_url}"
                else:
                    full_url = 'N/A'

                # Check for duplicates (FIXED)
                if full_url in existing_urls:
                    page_skip_count += 1
                    total_skipped += 1
                    print(f"⏭️  Property {i}/{len(properties)}: Skipped duplicate - {full_url}")
                    continue

                # New property - process it
                page_new_count += 1
                total_new_properties += 1

                print(f"\n🏠 Property {i}/{len(properties)} (New #{total_new_properties}):")
                print(f"📍 Address: {address}")
                print(f"💰 Price: {price}")
                print(f"🛏️  Beds: {beds} | 🚿 Baths: {baths} | 📐 Sqft: {sqft}")

                # Fetch agent details
                print(f"🔍 Fetching agent details...")
                agent_info = get_agent_info(detail_url)

                print(f"👤 Agent: {agent_info['agent_name']}")
                print(f"📞 Phone: {agent_info['agent_phone']}")
                print(f"🔗 URL: {full_url}")

                # Add to CSV immediately (URL moved to last column)
                add_property_to_csv(
                    address, beds, baths, sqft, price,
                    agent_info['agent_name'], agent_info['agent_phone'], full_url
                )

                # Add to existing URLs set
                existing_urls.add(full_url)

                print("✅ Added to CSV")
                print("-" * 50)

                # Small delay to be polite
                time.sleep(0.5)

            except Exception as e:
                print(f"❌ Error processing property {i}: {str(e)}")
                continue

        # Page summary
        print(f"\n📊 Page {page} Summary:")
        print(f"   🆕 New properties: {page_new_count}")
        print(f"   ⏭️  Skipped duplicates: {page_skip_count}")
        print(f"   📈 Total new so far: {total_new_properties}")
        print(f"   📈 Total skipped so far: {total_skipped}")

        page += 1

        # Small delay between pages
        time.sleep(1)

    # Final summary
    print("\n" + "=" * 60)
    print("🎉 SCRAPING COMPLETED!")
    print(f"📊 Final Statistics:")
    print(f"   🆕 Total new properties added: {total_new_properties}")
    print(f"   ⏭️  Total duplicates skipped: {total_skipped}")
    print(f"   📄 Total pages processed: {page - 1}")
    print(f"   📁 Data saved to: zillow_florida_data.csv")
    print("=" * 60)

def complete_missing_details():
    """Complete missing details for existing properties"""
    print("🔧 Starting Property Details Completion")
    print("=" * 60)

    # Read existing properties
    properties = []
    try:
        with open('zillow_florida_data.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                properties.append(row)
    except FileNotFoundError:
        print("❌ No CSV file found")
        return

    print(f"📋 Found {len(properties)} properties")

    # Find incomplete properties
    incomplete_properties = []
    for i, prop in enumerate(properties):
        missing_fields = []

        if not prop.get('Beds', '').strip() or prop.get('Beds', '').strip() == 'N/A':
            missing_fields.append('Beds')
        if not prop.get('Baths', '').strip() or prop.get('Baths', '').strip() == 'N/A':
            missing_fields.append('Baths')
        if not prop.get('Sqft', '').strip() or prop.get('Sqft', '').strip() == 'N/A':
            missing_fields.append('Sqft')
        if not prop.get('Agent_Name', '').strip() or prop.get('Agent_Name', '').strip() == 'N/A':
            missing_fields.append('Agent_Name')
        if not prop.get('Agent_Phone', '').strip() or prop.get('Agent_Phone', '').strip() == 'N/A':
            missing_fields.append('Agent_Phone')

        if missing_fields and prop.get('URL', '').strip():
            prop['missing_fields'] = missing_fields
            prop['index'] = i
            incomplete_properties.append(prop)

    print(f"⚠️  Found {len(incomplete_properties)} incomplete properties with URLs")

    if not incomplete_properties:
        print("✅ All properties are complete!")
        return

    # Ask how many to process
    max_to_process = input(f"\nHow many properties to complete? (1-{len(incomplete_properties)}, or 'all'): ").strip()

    if max_to_process.lower() == 'all':
        properties_to_process = incomplete_properties
    else:
        try:
            max_count = int(max_to_process)
            properties_to_process = incomplete_properties[:max_count]
        except:
            print("❌ Invalid input, processing first 10 properties")
            properties_to_process = incomplete_properties[:10]

    print(f"\n🔍 Processing {len(properties_to_process)} properties...")

    # Process properties
    completed_count = 0

    for i, prop in enumerate(properties_to_process, 1):
        address = prop.get('Address', '')
        url = prop.get('URL', '')
        missing = prop.get('missing_fields', [])
        prop_index = prop.get('index', 0)

        print(f"\n🏠 Property {i}/{len(properties_to_process)}")
        print(f"   📍 Address: {address}")
        print(f"   🔗 URL: {url[:80]}...")
        print(f"   ⚠️  Missing: {', '.join(missing)}")

        updated_fields = []

        # Get property details
        if 'Beds' in missing or 'Baths' in missing or 'Sqft' in missing:
            print(f"   🔍 Getting property details...")
            property_details = get_property_details(url)

            if property_details['beds'] != 'N/A' and 'Beds' in missing:
                properties[prop_index]['Beds'] = property_details['beds']
                updated_fields.append(f"Beds: {property_details['beds']}")

            if property_details['baths'] != 'N/A' and 'Baths' in missing:
                properties[prop_index]['Baths'] = property_details['baths']
                updated_fields.append(f"Baths: {property_details['baths']}")

            if property_details['sqft'] != 'N/A' and 'Sqft' in missing:
                properties[prop_index]['Sqft'] = property_details['sqft']
                updated_fields.append(f"Sqft: {property_details['sqft']}")

        # Get agent info
        if 'Agent_Name' in missing or 'Agent_Phone' in missing:
            print(f"   🔍 Getting agent info...")
            agent_info = get_agent_info(url)

            if agent_info['agent_name'] != 'N/A' and 'Agent_Name' in missing:
                properties[prop_index]['Agent_Name'] = agent_info['agent_name']
                updated_fields.append(f"Agent: {agent_info['agent_name']}")

            if agent_info['agent_phone'] != 'N/A' and 'Agent_Phone' in missing:
                properties[prop_index]['Agent_Phone'] = agent_info['agent_phone']
                updated_fields.append(f"Phone: {agent_info['agent_phone']}")

        if updated_fields:
            print(f"      ✅ Updated: {', '.join(updated_fields)}")
            completed_count += 1
        else:
            print(f"      ⚠️  No new data found")

        # Rate limiting
        if i < len(properties_to_process):
            print(f"   ⏳ Waiting 4 seconds...")
            time.sleep(4)

    # Save updated data
    print(f"\n💾 Saving updated data...")

    headers = ['Address', 'Beds', 'Baths', 'Sqft', 'Price', 'Agent_Name', 'Agent_Phone', 'URL']

    with open('zillow_florida_data.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()

        for prop in properties:
            clean_row = {}
            for header in headers:
                clean_row[header] = prop.get(header, '').strip()
            writer.writerow(clean_row)

    print(f"\n🎉 COMPLETION SUMMARY:")
    print(f"   🎯 Properties processed: {len(properties_to_process)}")
    print(f"   ✅ Successfully completed: {completed_count}")
    print(f"   📁 Updated file: zillow_florida_data.csv")

if __name__ == "__main__":
    print("🎯 Enhanced Working Zillow Scraper")
    print("✅ Your original working code + JSON extraction for more properties")
    print("✅ Fixed duplicate detection + all your original features")
    print("\n" + "=" * 60)
    print("Choose option:")
    print("1. Start new scraping")
    print("2. Complete missing details for existing properties")

    choice = input("\nEnter choice (1/2): ").strip()

    if choice == '1':
        scrape_zillow_complete()
    elif choice == '2':
        complete_missing_details()
    else:
        print("❌ Invalid choice")
