#!/usr/bin/env python3
"""
Pipedrive API Rate Limiter
Manages 10,000 daily API limit efficiently
"""

import time
import json
import threading
from datetime import datetime, timedelta
from collections import deque
import logging
from config import PIPEDRIVE_LIMITS

class PipedriveRateLimiter:
    def __init__(self):
        self.daily_limit = PIPEDRIVE_LIMITS['DAILY_LIMIT']
        self.hourly_limit = PIPEDRIVE_LIMITS['HOURLY_LIMIT']
        self.minute_limit = PIPEDRIVE_LIMITS['MINUTE_LIMIT']
        self.buffer = PIPEDRIVE_LIMITS['RATE_LIMIT_BUFFER']
        
        # Request tracking
        self.daily_requests = 0
        self.hourly_requests = deque()
        self.minute_requests = deque()
        
        # Priority queues
        self.high_priority_queue = deque()
        self.low_priority_queue = deque()
        
        # Tracking
        self.last_reset = datetime.now().date()
        self.lock = threading.Lock()
        
        # Load saved state
        self.load_state()
        
        logging.info(f"🚦 Rate Limiter initialized - Daily: {self.daily_requests}/{self.daily_limit}")
    
    def load_state(self):
        """Load rate limiter state from file"""
        try:
            with open('pipedrive_rate_state.json', 'r') as f:
                state = json.load(f)
                
                # Check if it's a new day
                last_date = datetime.fromisoformat(state.get('last_reset', datetime.now().isoformat())).date()
                
                if last_date == datetime.now().date():
                    self.daily_requests = state.get('daily_requests', 0)
                else:
                    self.daily_requests = 0  # Reset for new day
                    
                logging.info(f"📊 Loaded state: {self.daily_requests} requests used today")
                
        except FileNotFoundError:
            logging.info("📊 No previous state found, starting fresh")
        except Exception as e:
            logging.error(f"❌ Error loading state: {e}")
    
    def save_state(self):
        """Save current state to file"""
        try:
            state = {
                'daily_requests': self.daily_requests,
                'last_reset': datetime.now().isoformat(),
                'hourly_count': len(self.hourly_requests),
                'minute_count': len(self.minute_requests)
            }
            
            with open('pipedrive_rate_state.json', 'w') as f:
                json.dump(state, f, indent=2)
                
        except Exception as e:
            logging.error(f"❌ Error saving state: {e}")
    
    def cleanup_old_requests(self):
        """Remove old requests from tracking queues"""
        now = datetime.now()
        
        # Clean hourly requests (older than 1 hour)
        while self.hourly_requests and (now - self.hourly_requests[0]) > timedelta(hours=1):
            self.hourly_requests.popleft()
        
        # Clean minute requests (older than 1 minute)
        while self.minute_requests and (now - self.minute_requests[0]) > timedelta(minutes=1):
            self.minute_requests.popleft()
    
    def check_daily_reset(self):
        """Check if daily limit should be reset"""
        today = datetime.now().date()
        
        if today > self.last_reset:
            self.daily_requests = 0
            self.last_reset = today
            logging.info("🔄 Daily rate limit reset")
            self.save_state()
    
    def can_make_request(self, operation_type='normal'):
        """Check if a request can be made within limits"""
        with self.lock:
            self.cleanup_old_requests()
            self.check_daily_reset()
            
            # Apply buffer to limits
            daily_limit_buffered = int(self.daily_limit * self.buffer)
            hourly_limit_buffered = int(self.hourly_limit * self.buffer)
            minute_limit_buffered = int(self.minute_limit * self.buffer)
            
            # Check limits
            if self.daily_requests >= daily_limit_buffered:
                logging.warning(f"⚠️ Daily limit reached: {self.daily_requests}/{daily_limit_buffered}")
                return False
            
            if len(self.hourly_requests) >= hourly_limit_buffered:
                logging.warning(f"⚠️ Hourly limit reached: {len(self.hourly_requests)}/{hourly_limit_buffered}")
                return False
            
            if len(self.minute_requests) >= minute_limit_buffered:
                logging.warning(f"⚠️ Minute limit reached: {len(self.minute_requests)}/{minute_limit_buffered}")
                return False
            
            return True
    
    def record_request(self, operation_type='normal'):
        """Record a successful API request"""
        with self.lock:
            now = datetime.now()
            
            self.daily_requests += 1
            self.hourly_requests.append(now)
            self.minute_requests.append(now)
            
            # Save state periodically
            if self.daily_requests % 10 == 0:
                self.save_state()
            
            logging.debug(f"📊 Request recorded: {operation_type} - Daily: {self.daily_requests}/{self.daily_limit}")
    
    def wait_for_availability(self, operation_type='normal', max_wait=300):
        """Wait until a request can be made"""
        start_time = time.time()
        
        while not self.can_make_request(operation_type):
            if time.time() - start_time > max_wait:
                logging.error(f"❌ Timeout waiting for rate limit availability")
                return False
            
            # Calculate wait time
            wait_time = self.calculate_wait_time()
            
            if wait_time > 0:
                logging.info(f"⏳ Rate limit reached, waiting {wait_time:.1f} seconds...")
                time.sleep(min(wait_time, 60))  # Max 1 minute wait
            else:
                time.sleep(1)  # Short wait
        
        return True
    
    def calculate_wait_time(self):
        """Calculate optimal wait time based on current limits"""
        now = datetime.now()
        
        # Check minute limit
        if len(self.minute_requests) >= int(self.minute_limit * self.buffer):
            if self.minute_requests:
                oldest_minute = self.minute_requests[0]
                wait_until = oldest_minute + timedelta(minutes=1)
                return (wait_until - now).total_seconds()
        
        # Check hourly limit
        if len(self.hourly_requests) >= int(self.hourly_limit * self.buffer):
            if self.hourly_requests:
                oldest_hour = self.hourly_requests[0]
                wait_until = oldest_hour + timedelta(hours=1)
                return (wait_until - now).total_seconds()
        
        return 0
    
    def get_status(self):
        """Get current rate limiter status"""
        with self.lock:
            self.cleanup_old_requests()
            
            daily_used_pct = (self.daily_requests / self.daily_limit) * 100
            hourly_used_pct = (len(self.hourly_requests) / self.hourly_limit) * 100
            minute_used_pct = (len(self.minute_requests) / self.minute_limit) * 100
            
            return {
                'daily': {
                    'used': self.daily_requests,
                    'limit': self.daily_limit,
                    'percentage': daily_used_pct,
                    'remaining': self.daily_limit - self.daily_requests
                },
                'hourly': {
                    'used': len(self.hourly_requests),
                    'limit': self.hourly_limit,
                    'percentage': hourly_used_pct,
                    'remaining': self.hourly_limit - len(self.hourly_requests)
                },
                'minute': {
                    'used': len(self.minute_requests),
                    'limit': self.minute_limit,
                    'percentage': minute_used_pct,
                    'remaining': self.minute_limit - len(self.minute_requests)
                }
            }
    
    def is_high_priority(self, operation_type):
        """Check if operation is high priority"""
        return operation_type in PIPEDRIVE_LIMITS['PRIORITY_OPERATIONS']
    
    def queue_request(self, operation_type, request_data):
        """Queue a request based on priority"""
        request_item = {
            'operation_type': operation_type,
            'data': request_data,
            'timestamp': datetime.now(),
            'retries': 0
        }
        
        if self.is_high_priority(operation_type):
            self.high_priority_queue.append(request_item)
            logging.debug(f"📋 High priority request queued: {operation_type}")
        else:
            self.low_priority_queue.append(request_item)
            logging.debug(f"📋 Low priority request queued: {operation_type}")
    
    def get_next_request(self):
        """Get next request from queue (high priority first)"""
        if self.high_priority_queue:
            return self.high_priority_queue.popleft()
        elif self.low_priority_queue:
            return self.low_priority_queue.popleft()
        else:
            return None
    
    def process_queued_requests(self, processor_function):
        """Process queued requests with rate limiting"""
        processed = 0
        
        while True:
            request_item = self.get_next_request()
            
            if not request_item:
                break
            
            # Wait for availability
            if self.wait_for_availability(request_item['operation_type']):
                try:
                    # Process the request
                    result = processor_function(request_item)
                    
                    if result:
                        self.record_request(request_item['operation_type'])
                        processed += 1
                        logging.debug(f"✅ Processed: {request_item['operation_type']}")
                    else:
                        # Retry logic
                        request_item['retries'] += 1
                        if request_item['retries'] < 3:
                            self.queue_request(request_item['operation_type'], request_item['data'])
                            logging.warning(f"🔄 Retrying: {request_item['operation_type']}")
                        else:
                            logging.error(f"❌ Failed after retries: {request_item['operation_type']}")
                
                except Exception as e:
                    logging.error(f"❌ Error processing request: {e}")
            else:
                # Put request back in queue
                self.queue_request(request_item['operation_type'], request_item['data'])
                break
        
        return processed

def test_rate_limiter():
    """Test the rate limiter"""
    limiter = PipedriveRateLimiter()
    
    print("🧪 Testing Pipedrive Rate Limiter")
    print("=" * 40)
    
    # Show current status
    status = limiter.get_status()
    print(f"📊 Current Status:")
    print(f"   Daily: {status['daily']['used']}/{status['daily']['limit']} ({status['daily']['percentage']:.1f}%)")
    print(f"   Hourly: {status['hourly']['used']}/{status['hourly']['limit']} ({status['hourly']['percentage']:.1f}%)")
    print(f"   Minute: {status['minute']['used']}/{status['minute']['limit']} ({status['minute']['percentage']:.1f}%)")
    
    # Test request checking
    print(f"\n🔍 Testing request availability:")
    for operation in ['create_deal', 'add_note', 'search_deals']:
        can_make = limiter.can_make_request(operation)
        priority = "HIGH" if limiter.is_high_priority(operation) else "LOW"
        print(f"   {operation}: {'✅ Available' if can_make else '❌ Limited'} ({priority} priority)")
    
    # Simulate some requests
    print(f"\n📝 Simulating 5 requests...")
    for i in range(5):
        if limiter.can_make_request('test'):
            limiter.record_request('test')
            print(f"   Request {i+1}: ✅ Recorded")
        else:
            print(f"   Request {i+1}: ❌ Rate limited")
    
    # Final status
    final_status = limiter.get_status()
    print(f"\n📊 Final Status:")
    print(f"   Daily: {final_status['daily']['used']}/{final_status['daily']['limit']}")

if __name__ == "__main__":
    test_rate_limiter()
