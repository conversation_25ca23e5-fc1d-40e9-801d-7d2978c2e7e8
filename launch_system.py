#!/usr/bin/env python3
"""
System Launcher for Integrated Zillow System
Easy startup and management interface
"""

import os
import sys
import time
import threading
import logging
from datetime import datetime

# Import system components
try:
    from integrated_zillow_system import ZillowIntegratedSystem
    from config import get_config, validate_config
    from sms_response_handler import SMSResponseHandler
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure all required files are in the same directory:")
    print("  - integrated_zillow_system.py")
    print("  - config.py") 
    print("  - sms_response_handler.py")
    sys.exit(1)

class SystemLauncher:
    def __init__(self):
        self.config = get_config()
        self.system = None
        self.running = False
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=getattr(logging, self.config['system']['LOG_LEVEL']),
            format=log_format,
            handlers=[
                logging.FileHandler(self.config['files']['LOG_FILE']),
                logging.StreamHandler()
            ]
        )
        
        logging.info("🚀 System Launcher initialized")
    
    def validate_setup(self):
        """Validate system setup and configuration"""
        print("🔍 Validating system setup...")
        
        # Validate configuration
        errors = validate_config()
        if errors:
            print("❌ Configuration Errors:")
            for error in errors:
                print(f"   - {error}")
            return False
        
        # Check file permissions
        try:
            # Test CSV file write access
            csv_file = self.config['files']['CSV_FILE']
            with open(csv_file, 'a') as f:
                pass
            print(f"✅ CSV file access: {csv_file}")
        except Exception as e:
            print(f"❌ CSV file access error: {e}")
            return False
        
        # Test API connectivity
        if not self.test_api_connectivity():
            return False
        
        print("✅ System validation completed")
        return True
    
    def test_api_connectivity(self):
        """Test connectivity to external APIs"""
        print("🌐 Testing API connectivity...")
        
        import requests
        
        # Test ScraperAPI
        try:
            scraper_test_url = f"https://api.scraperapi.com/?api_key={self.config['api_keys']['scraper']}&url=https://httpbin.org/ip"
            response = requests.get(scraper_test_url, timeout=10)
            if response.status_code == 200:
                print("✅ ScraperAPI: Connected")
            else:
                print(f"⚠️ ScraperAPI: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ ScraperAPI: {str(e)}")
            return False
        
        # Test Pipedrive API
        try:
            pipedrive_test_url = f"{self.config['urls']['pipedrive']}/users/me?api_token={self.config['api_keys']['pipedrive']}"
            response = requests.get(pipedrive_test_url, timeout=10)
            if response.status_code == 200:
                print("✅ Pipedrive API: Connected")
            else:
                print(f"⚠️ Pipedrive API: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ Pipedrive API: {str(e)}")
            return False
        
        # SMS API test would go here
        print("✅ API connectivity test completed")
        return True
    
    def show_system_info(self):
        """Display system information"""
        print("\n" + "="*60)
        print("🎯 INTEGRATED ZILLOW SCRAPING & CRM SYSTEM")
        print("="*60)
        print("📋 FEATURES:")
        print("  ✅ Continuous property scraping with duplicate checking")
        print("  ✅ Infinite scroll for maximum property discovery")
        print("  ✅ Pipedrive CRM integration (On Market pipeline)")
        print("  ✅ Automated SMS messaging to property agents")
        print("  ✅ Response tracking and auto-reply system")
        print("  ✅ Multi-threaded processing for optimal performance")
        print("  ✅ Real-time logging and monitoring")
        print("\n📊 CONFIGURATION:")
        print(f"  🔑 API Keys: Configured")
        print(f"  📱 Client Phone: {self.config['phone']['client']}")
        print(f"  🧵 Total Threads: {sum(self.config['threads'].values())}")
        print(f"  📄 CSV Output: {self.config['files']['CSV_FILE']}")
        print(f"  📝 Log File: {self.config['files']['LOG_FILE']}")
        print(f"  🎯 Max Properties/Run: {self.config['scraping']['MAX_PROPERTIES_PER_RUN']}")
        print("="*60)
    
    def show_menu(self):
        """Show main menu"""
        print("\n🚀 SYSTEM LAUNCHER MENU")
        print("-" * 30)
        print("1. 🏃 Start Full System")
        print("2. 🔧 Start Scraper Only")
        print("3. 📱 Start SMS Monitor Only")
        print("4. 📊 View System Status")
        print("5. 🧪 Test Configuration")
        print("6. 📋 View Logs")
        print("7. ⚙️  System Settings")
        print("8. 🛑 Exit")
        print("-" * 30)
    
    def start_full_system(self):
        """Start the complete integrated system"""
        print("\n🚀 Starting Full Integrated System...")
        
        try:
            self.system = ZillowIntegratedSystem()
            self.running = True
            
            # Start system in separate thread
            system_thread = threading.Thread(target=self.system.start_system, daemon=True)
            system_thread.start()
            
            print("✅ System started successfully!")
            print("📊 Monitor the logs for real-time updates")
            print("🛑 Press Ctrl+C to stop the system")
            
            # Keep main thread alive and show status
            try:
                while self.running:
                    time.sleep(10)
                    self.show_runtime_status()
            except KeyboardInterrupt:
                print("\n🛑 Stopping system...")
                self.running = False
                self.system.running = False
                print("✅ System stopped")
                
        except Exception as e:
            print(f"❌ Error starting system: {e}")
            logging.error(f"System startup error: {e}")
    
    def start_scraper_only(self):
        """Start only the scraping component"""
        print("\n🔧 Starting Scraper Only...")
        
        try:
            self.system = ZillowIntegratedSystem()
            
            # Start only scraping threads
            scraper_thread = threading.Thread(target=self.system.scraping_worker, daemon=True)
            processor_thread = threading.Thread(target=self.system.property_processor_worker, daemon=True)
            
            scraper_thread.start()
            processor_thread.start()
            
            print("✅ Scraper started successfully!")
            print("🛑 Press Ctrl+C to stop")
            
            try:
                while True:
                    time.sleep(10)
                    print(f"📊 Queue Status - Properties: {self.system.property_queue.qsize()}")
            except KeyboardInterrupt:
                print("\n🛑 Stopping scraper...")
                
        except Exception as e:
            print(f"❌ Error starting scraper: {e}")
    
    def start_sms_monitor_only(self):
        """Start only the SMS monitoring component"""
        print("\n📱 Starting SMS Monitor Only...")
        
        try:
            handler = SMSResponseHandler(
                self.config['api_keys']['pipedrive'],
                self.config['api_keys']['sms']
            )
            
            print("✅ SMS Monitor started successfully!")
            print("🛑 Press Ctrl+C to stop")
            
            handler.monitor_responses()
            
        except Exception as e:
            print(f"❌ Error starting SMS monitor: {e}")
    
    def show_runtime_status(self):
        """Show runtime status"""
        if self.system:
            try:
                prop_queue_size = self.system.property_queue.qsize()
                sms_queue_size = self.system.sms_queue.qsize()
                
                print(f"📊 {datetime.now().strftime('%H:%M:%S')} - "
                      f"Properties: {prop_queue_size}, SMS: {sms_queue_size}, "
                      f"Existing: {len(self.system.existing_properties)}")
                      
            except Exception as e:
                logging.error(f"Status update error: {e}")
    
    def view_logs(self):
        """View recent log entries"""
        print("\n📋 Recent Log Entries:")
        print("-" * 50)
        
        try:
            with open(self.config['files']['LOG_FILE'], 'r') as f:
                lines = f.readlines()
                # Show last 20 lines
                for line in lines[-20:]:
                    print(line.strip())
        except FileNotFoundError:
            print("No log file found")
        except Exception as e:
            print(f"Error reading logs: {e}")
    
    def test_configuration(self):
        """Test system configuration"""
        print("\n🧪 Testing Configuration...")
        
        # Run validation
        if self.validate_setup():
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed")
    
    def run(self):
        """Main launcher loop"""
        self.show_system_info()
        
        # Initial validation
        if not self.validate_setup():
            print("❌ System validation failed. Please fix errors before continuing.")
            return
        
        while True:
            try:
                self.show_menu()
                choice = input("\n👉 Enter your choice (1-8): ").strip()
                
                if choice == '1':
                    self.start_full_system()
                elif choice == '2':
                    self.start_scraper_only()
                elif choice == '3':
                    self.start_sms_monitor_only()
                elif choice == '4':
                    self.show_runtime_status()
                elif choice == '5':
                    self.test_configuration()
                elif choice == '6':
                    self.view_logs()
                elif choice == '7':
                    print("⚙️ Settings menu coming soon...")
                elif choice == '8':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid choice. Please try again.")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

def main():
    """Main entry point"""
    launcher = SystemLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
