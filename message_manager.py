#!/usr/bin/env python3
"""
Message Manager for Custom SMS Templates and Configuration
"""

import json
import os
from datetime import datetime
from config import MESSAGE_TEMPLATES, MESSAGE_CONFIG, SENDER_PHONE

class MessageManager:
    def __init__(self):
        self.templates = MESSAGE_TEMPLATES.copy()
        self.config = MESSAGE_CONFIG.copy()
        self.sender_phone = SENDER_PHONE
        self.custom_templates_file = 'custom_message_templates.json'
        self.sender_config_file = 'sender_config.json'
        
        # Load custom templates and sender config
        self.load_custom_templates()
        self.load_sender_config()
    
    def load_custom_templates(self):
        """Load custom message templates from file"""
        try:
            if os.path.exists(self.custom_templates_file):
                with open(self.custom_templates_file, 'r', encoding='utf-8') as f:
                    custom_templates = json.load(f)
                    self.templates.update(custom_templates)
                print(f"✅ Loaded {len(custom_templates)} custom templates")
        except Exception as e:
            print(f"⚠️ Error loading custom templates: {e}")
    
    def load_sender_config(self):
        """Load sender configuration from file"""
        try:
            if os.path.exists(self.sender_config_file):
                with open(self.sender_config_file, 'r', encoding='utf-8') as f:
                    sender_config = json.load(f)
                    self.sender_phone = sender_config.get('sender_phone', self.sender_phone)
                    self.config.update(sender_config.get('message_config', {}))
                print(f"✅ Loaded sender config: {self.sender_phone}")
        except Exception as e:
            print(f"⚠️ Error loading sender config: {e}")
    
    def save_custom_templates(self):
        """Save custom templates to file"""
        try:
            # Only save templates that are not in default templates
            custom_only = {k: v for k, v in self.templates.items() 
                          if k not in MESSAGE_TEMPLATES}
            
            with open(self.custom_templates_file, 'w', encoding='utf-8') as f:
                json.dump(custom_only, f, indent=2, ensure_ascii=False)
            print(f"✅ Saved {len(custom_only)} custom templates")
        except Exception as e:
            print(f"❌ Error saving custom templates: {e}")
    
    def save_sender_config(self):
        """Save sender configuration to file"""
        try:
            config_data = {
                'sender_phone': self.sender_phone,
                'message_config': self.config,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.sender_config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            print(f"✅ Saved sender config")
        except Exception as e:
            print(f"❌ Error saving sender config: {e}")
    
    def get_available_templates(self):
        """Get list of available message templates"""
        return list(self.templates.keys())
    
    def get_template(self, template_name):
        """Get a specific message template"""
        template = self.templates.get(template_name)
        if template is None:
            # Fallback to INITIAL_CONTACT
            template = self.templates.get('INITIAL_CONTACT')
        if template is None:
            # Ultimate fallback
            template = "Hello! Inquiring on {address}. I'm Prepared to offer {offer_amount} all cash, quick close. Would it make sense to write up an offer? Please advise on next steps."
        return template
    
    def add_custom_template(self, name, template):
        """Add a new custom message template"""
        self.templates[name] = template
        self.save_custom_templates()
        print(f"✅ Added custom template: {name}")
    
    def is_rental_price(self, price_str):
        """Check if price indicates rental property"""
        if not price_str:
            return False

        rental_indicators = ['/mo', '/month', 'per month', 'monthly', 'rent']
        price_lower = price_str.lower()

        for indicator in rental_indicators:
            if indicator in price_lower:
                return True

        return False

    def format_message(self, template_name, **kwargs):
        """Format message with property details and offer calculations"""
        template = self.get_template(template_name)

        # Calculate offer amounts if needed
        offer_amount = kwargs.get('offer_amount', '')
        offer_percentage = kwargs.get('offer_percentage', 65)  # Default 65%

        # If price is provided and no offer_amount, calculate it
        if not offer_amount and kwargs.get('price'):
            price_str = kwargs.get('price', '')

            # Check if this is a rental property (skip calculation)
            if self.is_rental_price(price_str):
                print(f"⚠️ Rental property detected - skipping offer calculation: {price_str}")
                return "Rental property - please contact for details"
            else:
                try:
                    # Extract numeric value from price
                    import re
                    price_numbers = re.findall(r'[\d,]+', price_str.replace('$', '').replace(',', ''))
                    if price_numbers:
                        listing_price = int(price_numbers[0])
                        calculated_offer = int(listing_price * (offer_percentage / 100))
                        offer_amount = f"${calculated_offer:,}"
                except:
                    offer_amount = "Competitive offer"

        # Default values
        defaults = {
            'agent_name': kwargs.get('agent_name', 'Agent'),
            'address': kwargs.get('address', 'Property'),
            'price': kwargs.get('price', 'Listed Price'),
            'beds': kwargs.get('beds', ''),
            'baths': kwargs.get('baths', ''),
            'sqft': kwargs.get('sqft', ''),
            'sender_name': kwargs.get('sender_name', 'Buyer'),
            'sender_phone': self.sender_phone,
            'offer_amount': offer_amount,
            'offer_percentage': str(offer_percentage)
        }

        # Update with provided kwargs
        defaults.update(kwargs)

        try:
            formatted_message = template.format(**defaults)

            # Check message length
            if len(formatted_message) > self.config.get('MAX_MESSAGE_LENGTH', 160):
                print(f"⚠️ Message length ({len(formatted_message)}) exceeds limit")

            return formatted_message

        except KeyError as e:
            print(f"❌ Missing template variable: {e}")
            return template
    
    def interactive_message_setup(self):
        """Interactive setup for custom messages and sender info"""
        print("\n🎯 MESSAGE & SENDER CONFIGURATION")
        print("=" * 50)

        # Configure sender phone
        print(f"\n📱 Current Sender Phone: {self.sender_phone}")
        new_phone = input("Enter new sender phone (or press Enter to keep current): ").strip()

        if new_phone:
            # Validate phone format
            if new_phone.startswith('+') and len(new_phone) >= 10:
                self.sender_phone = new_phone
                print(f"✅ Sender phone updated: {self.sender_phone}")
            else:
                print("❌ Invalid phone format. Use international format (+1234567890)")

        # Configure offer percentage
        print(f"\n💰 Current Offer Percentage: {self.config.get('OFFER_PERCENTAGE', 65)}%")
        new_percentage = input("Enter offer percentage (or press Enter to keep current): ").strip()

        if new_percentage:
            try:
                percentage = float(new_percentage)
                if 1 <= percentage <= 100:
                    self.config['OFFER_PERCENTAGE'] = percentage
                    print(f"✅ Offer percentage updated: {percentage}%")
                else:
                    print("❌ Percentage must be between 1 and 100")
            except ValueError:
                print("❌ Invalid percentage format")

        # Show available templates
        print(f"\n📝 Available Message Templates:")
        for i, template_name in enumerate(self.get_available_templates(), 1):
            template = self.get_template(template_name)
            print(f"{i}. {template_name}")
            print(f"   Preview: {template[:80]}...")

        # Template selection
        print(f"\nOptions:")
        print("1. Use existing template")
        print("2. Create new custom template")
        print("3. Edit existing template")
        print("4. Test message formatting")
        print("5. Configure offer settings")

        choice = input("\nEnter choice (1-5): ").strip()

        if choice == '1':
            self.select_default_template()
        elif choice == '2':
            self.create_custom_template()
        elif choice == '3':
            self.edit_template()
        elif choice == '4':
            self.test_message_formatting()
        elif choice == '5':
            self.configure_offer_settings()

        # Save configuration
        self.save_sender_config()

        return {
            'sender_phone': self.sender_phone,
            'default_template': self.config.get('DEFAULT_TEMPLATE'),
            'offer_percentage': self.config.get('OFFER_PERCENTAGE'),
            'templates': self.templates
        }
    
    def select_default_template(self):
        """Select default template for messaging"""
        templates = self.get_available_templates()
        
        print("\nSelect default template:")
        for i, name in enumerate(templates, 1):
            print(f"{i}. {name}")
        
        try:
            choice = int(input("Enter template number: ")) - 1
            if 0 <= choice < len(templates):
                self.config['DEFAULT_TEMPLATE'] = templates[choice]
                print(f"✅ Default template set: {templates[choice]}")
            else:
                print("❌ Invalid choice")
        except ValueError:
            print("❌ Invalid input")
    
    def create_custom_template(self):
        """Create a new custom message template"""
        print("\n📝 Create Custom Template")
        print("Available variables: {agent_name}, {address}, {price}, {beds}, {baths}, {sqft}, {sender_name}")
        
        name = input("Template name: ").strip().upper().replace(' ', '_')
        if not name:
            print("❌ Template name required")
            return
        
        print("Enter message template (use variables like {agent_name}):")
        template = input().strip()
        
        if not template:
            print("❌ Template content required")
            return
        
        # Test template
        try:
            test_message = template.format(
                agent_name="John Doe",
                address="123 Main St",
                price="$250,000",
                beds="3bd",
                baths="2ba",
                sqft="1,500sqft",
                sender_name="Buyer"
            )
            
            print(f"\n📱 Preview: {test_message}")
            
            confirm = input("Save this template? (y/n): ").strip().lower()
            if confirm == 'y':
                self.add_custom_template(name, template)
            
        except Exception as e:
            print(f"❌ Template error: {e}")
    
    def edit_template(self):
        """Edit existing template"""
        templates = self.get_available_templates()
        
        print("\nSelect template to edit:")
        for i, name in enumerate(templates, 1):
            print(f"{i}. {name}")
        
        try:
            choice = int(input("Enter template number: ")) - 1
            if 0 <= choice < len(templates):
                template_name = templates[choice]
                current_template = self.get_template(template_name)
                
                print(f"\nCurrent template: {current_template}")
                new_template = input("Enter new template (or press Enter to keep current): ").strip()
                
                if new_template:
                    self.templates[template_name] = new_template
                    self.save_custom_templates()
                    print(f"✅ Template updated: {template_name}")
            else:
                print("❌ Invalid choice")
        except ValueError:
            print("❌ Invalid input")
    
    def test_message_formatting(self):
        """Test message formatting with sample data"""
        templates = self.get_available_templates()

        print("\nSelect template to test:")
        for i, name in enumerate(templates, 1):
            print(f"{i}. {name}")

        try:
            choice = int(input("Enter template number: ")) - 1
            if 0 <= choice < len(templates):
                template_name = templates[choice]

                # Sample data
                sample_data = {
                    'agent_name': 'Sarah Johnson',
                    'address': '456 Oak Avenue, Tampa, FL 33601',
                    'price': '$325,000',
                    'beds': '3bd',
                    'baths': '2ba',
                    'sqft': '1,850sqft',
                    'sender_name': 'Mike Smith',
                    'offer_percentage': self.config.get('OFFER_PERCENTAGE', 65)
                }

                formatted = self.format_message(template_name, **sample_data)

                print(f"\n📱 Formatted Message:")
                print(f"From: {self.sender_phone}")
                print(f"Template: {template_name}")
                print(f"Message: {formatted}")
                print(f"Length: {len(formatted)} characters")

                # Show offer calculation if applicable
                if 'offer_amount' in formatted:
                    print(f"💰 Offer Calculation: {self.config.get('OFFER_PERCENTAGE', 65)}% of $325,000 = ${int(325000 * 0.65):,}")

            else:
                print("❌ Invalid choice")
        except ValueError:
            print("❌ Invalid input")

    def configure_offer_settings(self):
        """Configure offer calculation settings"""
        print("\n💰 OFFER SETTINGS CONFIGURATION")
        print("-" * 40)

        current_percentage = self.config.get('OFFER_PERCENTAGE', 65)
        print(f"Current offer percentage: {current_percentage}%")

        print("\nOffer calculation examples:")
        for price in [200000, 300000, 500000]:
            offer = int(price * (current_percentage / 100))
            print(f"  ${price:,} → ${offer:,} ({current_percentage}%)")

        # Update percentage
        new_percentage = input(f"\nEnter new percentage (1-100) or press Enter to keep {current_percentage}%: ").strip()

        if new_percentage:
            try:
                percentage = float(new_percentage)
                if 1 <= percentage <= 100:
                    self.config['OFFER_PERCENTAGE'] = percentage
                    print(f"✅ Offer percentage updated: {percentage}%")

                    # Show new examples
                    print(f"\nNew calculation examples:")
                    for price in [200000, 300000, 500000]:
                        offer = int(price * (percentage / 100))
                        print(f"  ${price:,} → ${offer:,} ({percentage}%)")
                else:
                    print("❌ Percentage must be between 1 and 100")
            except ValueError:
                print("❌ Invalid percentage format")

        # Configure specific offer amounts
        print(f"\nSpecific offer configuration:")
        print("You can also set specific offer amounts for certain price ranges")

        configure_specific = input("Configure specific offers? (y/n): ").strip().lower()
        if configure_specific == 'y':
            self.configure_specific_offers()

    def configure_specific_offers(self):
        """Configure specific offer amounts for price ranges"""
        print("\n🎯 SPECIFIC OFFER CONFIGURATION")
        print("-" * 40)

        if 'SPECIFIC_OFFERS' not in self.config:
            self.config['SPECIFIC_OFFERS'] = {}

        print("Configure offers for specific price ranges:")
        print("Format: min_price-max_price = offer_amount")
        print("Example: 200000-300000 = 180000")

        while True:
            range_input = input("\nEnter price range (min-max) or 'done' to finish: ").strip()

            if range_input.lower() == 'done':
                break

            try:
                if '-' in range_input:
                    min_price, max_price = map(int, range_input.split('-'))
                    offer_amount = input(f"Offer amount for ${min_price:,}-${max_price:,}: $").strip()

                    if offer_amount.isdigit():
                        offer_amount = int(offer_amount)
                        range_key = f"{min_price}-{max_price}"
                        self.config['SPECIFIC_OFFERS'][range_key] = offer_amount

                        percentage = (offer_amount / ((min_price + max_price) / 2)) * 100
                        print(f"✅ Added: ${min_price:,}-${max_price:,} → ${offer_amount:,} (~{percentage:.1f}%)")
                    else:
                        print("❌ Invalid offer amount")
                else:
                    print("❌ Invalid format. Use: min-max")
            except ValueError:
                print("❌ Invalid price range format")

        print(f"\n📋 Configured specific offers:")
        for range_key, offer in self.config.get('SPECIFIC_OFFERS', {}).items():
            print(f"  {range_key}: ${offer:,}")

def main():
    """Main function for interactive message setup"""
    manager = MessageManager()
    config = manager.interactive_message_setup()
    
    print(f"\n🎉 Configuration Complete!")
    print(f"📱 Sender Phone: {config['sender_phone']}")
    print(f"📝 Default Template: {config['default_template']}")
    print(f"📋 Total Templates: {len(config['templates'])}")

if __name__ == "__main__":
    main()
