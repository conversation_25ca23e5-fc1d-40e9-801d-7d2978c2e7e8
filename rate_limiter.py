#!/usr/bin/env python3
"""
Pipedrive API Rate Limiter
Manages API request limits to prevent exceeding quotas
"""

import time
import json
import os
import logging
from datetime import datetime, timedelta

class PipedriveRateLimiter:
    """Rate limiter for Pipedrive API requests"""
    
    def __init__(self, daily_limit=10000, minute_limit=5, state_file='rate_limiter_state.json'):
        self.daily_limit = daily_limit
        self.minute_limit = minute_limit
        self.state_file = state_file
        
        # Initialize counters
        self.daily_requests = 0
        self.minute_requests = 0
        self.last_reset_date = datetime.now().date()
        self.minute_start = datetime.now().replace(second=0, microsecond=0)
        
        # Load existing state
        self.load_state()
        
        logging.info(f"🚦 Rate Limiter initialized - Daily: {self.daily_requests}/{self.daily_limit}")
    
    def load_state(self):
        """Load rate limiter state from file"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                
                # Check if it's a new day
                saved_date = datetime.fromisoformat(state.get('date', datetime.now().isoformat())).date()
                
                if saved_date == datetime.now().date():
                    # Same day, load existing counts
                    self.daily_requests = state.get('daily_requests', 0)
                    logging.info(f"📊 Loaded state: {self.daily_requests} requests used today")
                else:
                    # New day, reset counters
                    self.daily_requests = 0
                    logging.info(f"📅 New day detected - reset daily counter")
                    
        except Exception as e:
            logging.warning(f"⚠️ Could not load rate limiter state: {str(e)}")
            self.daily_requests = 0
    
    def save_state(self):
        """Save rate limiter state to file"""
        try:
            state = {
                'date': datetime.now().isoformat(),
                'daily_requests': self.daily_requests,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
                
        except Exception as e:
            logging.warning(f"⚠️ Could not save rate limiter state: {str(e)}")
    
    def reset_if_needed(self):
        """Reset counters if time periods have passed"""
        now = datetime.now()
        
        # Reset daily counter if new day
        if now.date() > self.last_reset_date:
            self.daily_requests = 0
            self.last_reset_date = now.date()
            logging.info(f"📅 Daily counter reset")
        
        # Reset minute counter if new minute
        current_minute = now.replace(second=0, microsecond=0)
        if current_minute > self.minute_start:
            self.minute_requests = 0
            self.minute_start = current_minute
    
    def can_make_request(self):
        """Check if we can make a request now"""
        self.reset_if_needed()
        
        # Check daily limit
        if self.daily_requests >= self.daily_limit:
            return False, f"Daily limit reached ({self.daily_requests}/{self.daily_limit})"
        
        # Check minute limit
        if self.minute_requests >= self.minute_limit:
            seconds_until_next_minute = 60 - datetime.now().second
            return False, f"Minute limit reached ({self.minute_requests}/{self.minute_limit}), wait {seconds_until_next_minute}s"
        
        return True, "OK"
    
    def record_request(self):
        """Record that a request was made"""
        self.reset_if_needed()
        
        self.daily_requests += 1
        self.minute_requests += 1
        
        # Save state periodically
        if self.daily_requests % 10 == 0:
            self.save_state()
        
        # Log warnings at certain thresholds
        if self.daily_requests >= self.daily_limit * 0.9:
            logging.warning(f"⚠️ Daily limit 90% reached: {self.daily_requests}/{self.daily_limit}")
        elif self.daily_requests >= self.daily_limit * 0.8:
            logging.warning(f"⚠️ Daily limit 80% reached: {self.daily_requests}/{self.daily_limit}")
    
    def wait_if_needed(self):
        """Wait if rate limit is reached"""
        can_proceed, message = self.can_make_request()
        
        if not can_proceed:
            if "Minute limit" in message:
                # Extract wait time from message
                wait_time = 60 - datetime.now().second
                logging.info(f"⏳ Rate limit reached, waiting {wait_time} seconds...")
                time.sleep(wait_time)
                
                # Check again after waiting
                return self.can_make_request()
            else:
                # Daily limit reached
                logging.error(f"❌ {message}")
                return False, message
        
        return True, "OK"
    
    def get_stats(self):
        """Get current rate limiter statistics"""
        self.reset_if_needed()
        
        return {
            'daily_requests': self.daily_requests,
            'daily_limit': self.daily_limit,
            'daily_remaining': self.daily_limit - self.daily_requests,
            'daily_percentage': (self.daily_requests / self.daily_limit) * 100,
            'minute_requests': self.minute_requests,
            'minute_limit': self.minute_limit,
            'minute_remaining': self.minute_limit - self.minute_requests
        }
    
    def __del__(self):
        """Save state when object is destroyed"""
        try:
            self.save_state()
        except:
            pass

# Test function
def test_rate_limiter():
    """Test the rate limiter"""
    limiter = PipedriveRateLimiter(daily_limit=100, minute_limit=3)
    
    print("Testing rate limiter...")
    
    # Test normal requests
    for i in range(5):
        can_proceed, message = limiter.can_make_request()
        print(f"Request {i+1}: {can_proceed} - {message}")
        
        if can_proceed:
            limiter.record_request()
        else:
            print(f"Waiting...")
            limiter.wait_if_needed()
    
    # Print stats
    stats = limiter.get_stats()
    print(f"Stats: {stats}")

if __name__ == "__main__":
    test_rate_limiter()
