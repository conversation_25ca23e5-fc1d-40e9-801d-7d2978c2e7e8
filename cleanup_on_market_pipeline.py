#!/usr/bin/env python3
"""
Cleanup On Market Pipeline - Delete All Leads
COMPLETED: Successfully deleted 84 deals on 2025-06-30
"""

import requests
import json
import time
from config import PIPEDRIVE_TOKEN, PIPEDRIVE_PIPELINE_ID

def get_all_on_market_deals():
    """Get all deals from On Market pipeline"""
    print(f"\n📊 FETCHING ALL DEALS FROM ON MARKET PIPELINE")
    print(f"="*60)
    
    all_deals = []
    start = 0
    limit = 100
    
    while True:
        try:
            url = f"https://api.pipedrive.com/v1/deals"
            params = {
                'api_token': PIPEDRIVE_TOKEN,
                'start': start,
                'limit': limit,
                'pipeline_id': PIPEDRIVE_PIPELINE_ID,  # On Market pipeline only
                'status': 'open'
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                deals = data.get('data', [])
                
                if not deals:
                    break
                
                all_deals.extend(deals)
                print(f"   Fetched {len(deals)} deals (Total: {len(all_deals)})")
                
                # Check if more data available
                additional_data = data.get('additional_data', {})
                pagination = additional_data.get('pagination', {})
                if not pagination.get('more_items_in_collection', False):
                    break
                
                start += limit
                time.sleep(0.5)  # Rate limiting
                
            else:
                print(f"❌ Error fetching deals: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            break
    
    print(f"\n📈 TOTAL ON MARKET DEALS FOUND: {len(all_deals)}")
    return all_deals

def delete_all_deals(deals):
    """Delete all deals from On Market pipeline"""
    print(f"\n🗑️ DELETING ALL {len(deals)} DEALS FROM ON MARKET PIPELINE")
    print(f"="*60)
    
    if not deals:
        print(f"✅ No deals to delete")
        return True
    
    deleted_count = 0
    failed_count = 0
    
    for i, deal in enumerate(deals, 1):
        try:
            deal_id = deal.get('id')
            title = deal.get('title', 'No Title')
            
            print(f"   Deleting {i}/{len(deals)}: {title[:50]}... (ID: {deal_id})")
            
            url = f"https://api.pipedrive.com/v1/deals/{deal_id}"
            params = {'api_token': PIPEDRIVE_TOKEN}
            
            response = requests.delete(url, params=params)
            
            if response.status_code == 200:
                print(f"   ✅ Deleted Deal ID {deal_id}")
                deleted_count += 1
            else:
                print(f"   ❌ Failed to delete Deal ID {deal_id}: {response.status_code}")
                failed_count += 1
            
            # Rate limiting - pause every 5 deletions
            if i % 5 == 0:
                print(f"   ⏱️ Pausing for rate limiting...")
                time.sleep(2)
            else:
                time.sleep(0.5)
            
        except Exception as e:
            print(f"   ❌ Error deleting Deal ID {deal.get('id', 'Unknown')}: {str(e)}")
            failed_count += 1
    
    print(f"\n📊 DELETION RESULTS:")
    print(f"   Successfully Deleted: {deleted_count}")
    print(f"   Failed to Delete: {failed_count}")
    print(f"   Total Processed: {len(deals)}")
    
    return deleted_count == len(deals)

def cleanup_on_market_pipeline():
    """Complete cleanup of On Market pipeline"""
    print(f"\n🧹 ON MARKET PIPELINE CLEANUP")
    print(f"="*60)
    
    print(f"⚠️ WARNING: This will delete ALL deals from On Market pipeline!")
    print(f"⚠️ Pipeline ID: {PIPEDRIVE_PIPELINE_ID}")
    print(f"⚠️ This action cannot be undone!")
    
    confirm = input(f"\nType 'DELETE ALL ON MARKET' to confirm: ").strip()
    
    if confirm == 'DELETE ALL ON MARKET':
        print(f"\n🔍 Starting cleanup process...")
        
        # Get all deals
        deals = get_all_on_market_deals()
        
        if not deals:
            print(f"\n✅ ON MARKET PIPELINE IS ALREADY CLEAN!")
            return True
        
        # Show summary before deletion
        print(f"\n📋 DEALS TO BE DELETED:")
        for i, deal in enumerate(deals[:10], 1):  # Show first 10
            print(f"   {i}. {deal.get('title', 'No Title')[:60]}...")
        
        if len(deals) > 10:
            print(f"   ... and {len(deals) - 10} more deals")
        
        final_confirm = input(f"\n⚠️ Proceed with deleting {len(deals)} deals? (y/n): ").strip().lower()
        
        if final_confirm == 'y':
            # Delete all deals
            success = delete_all_deals(deals)
            
            if success:
                print(f"\n🎉 ON MARKET PIPELINE CLEANUP SUCCESSFUL!")
                print(f"✅ All {len(deals)} deals deleted")
                print(f"✅ Pipeline is now clean")
                print(f"✅ Ready for fresh data")
                return True
            else:
                print(f"\n⚠️ Some deals could not be deleted")
                return False
        else:
            print(f"\n👋 Deletion cancelled")
            return False
    else:
        print(f"\n👋 Cleanup cancelled")
        return False

def verify_cleanup():
    """Verify that pipeline is clean"""
    print(f"\n🔍 VERIFYING PIPELINE CLEANUP")
    print(f"="*40)
    
    deals = get_all_on_market_deals()
    
    if not deals:
        print(f"\n✅ VERIFICATION SUCCESSFUL!")
        print(f"✅ On Market pipeline is completely clean")
        print(f"✅ No deals remaining")
        return True
    else:
        print(f"\n⚠️ VERIFICATION FAILED!")
        print(f"⚠️ {len(deals)} deals still remain")
        return False

def main():
    """Main function"""
    print(f"\n🧹 ON MARKET PIPELINE CLEANUP TOOL")
    print(f"Delete all deals from On Market pipeline")
    
    choice = input(f"\n🚀 Start On Market pipeline cleanup? (y/n): ").strip().lower()
    
    if choice == 'y':
        success = cleanup_on_market_pipeline()
        
        if success:
            # Verify cleanup
            verify_cleanup()
            
            print(f"\n🎯 CLEANUP COMPLETED!")
            print(f"✅ On Market pipeline is now clean")
            print(f"✅ Ready for new property data")
            print(f"✅ System can start fresh")
        else:
            print(f"\n⚠️ Cleanup had issues")
    else:
        print(f"👋 Cleanup cancelled")

if __name__ == "__main__":
    main()
