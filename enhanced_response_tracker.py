#!/usr/bin/env python3
"""
Enhanced Response Tracker for Integrated System
Automatically tracks SMS responses and updates Pipedrive stages
"""

import requests
import json
import time
import logging
import csv
from datetime import datetime, timedelta
import urllib.parse
from config import PIPEDRIVE_TOKEN, SMS_API_TOKEN, PIPEDRIVE_STAGES

class EnhancedResponseTracker:
    def __init__(self):
        self.pipedrive_token = PIPEDRIVE_TOKEN
        self.sms_token = SMS_API_TOKEN
        self.stages = PIPEDRIVE_STAGES
        self.base_url = "https://api.pipedrive.com/v1"
        self.sms_api_url = "https://humilix.com/sms/services/send.php"
        self.working_device_config = "9|1"  # CONFIRMED WORKING - OP5312L1 SIM 1
        
        # Track sent messages and responses
        self.sent_messages = {}  # {phone: {deal_id, sent_time, message_id}}
        self.processed_responses = set()  # Set of processed message IDs
        
        # Load existing sent messages
        self.load_sent_messages()
        
        print(f"\n📱 ENHANCED RESPONSE TRACKER")
        print(f"="*50)
        print(f"🔑 SMS Token: {self.sms_token[:10]}...")
        print(f"📊 Pipedrive Token: {self.pipedrive_token[:10]}...")
        print(f"📋 Loaded {len(self.sent_messages)} sent messages to track")

    def load_sent_messages(self):
        """Load sent messages from CSV for tracking"""
        try:
            # Load from zillow_florida_data.csv to get agent phones
            with open('zillow_florida_data.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    agent_phone = row.get('Agent_Phone', '').strip()
                    if agent_phone and agent_phone.startswith('+'):
                        # Search for deal by address
                        deal_id = self.find_deal_by_address(row.get('Address', ''))
                        if deal_id:
                            self.sent_messages[agent_phone] = {
                                'deal_id': deal_id,
                                'address': row.get('Address', ''),
                                'agent_name': row.get('Agent_Name', ''),
                                'sent_time': datetime.now().isoformat(),
                                'status': 'sent'
                            }
        except Exception as e:
            logging.warning(f"Could not load sent messages: {str(e)}")

    def find_deal_by_address(self, address):
        """Find Pipedrive deal by property address"""
        try:
            if not address:
                return None
                
            search_url = f"{self.base_url}/deals/search"
            params = {
                'term': address,
                'api_token': self.pipedrive_token
            }
            
            response = requests.get(search_url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                items = data.get('data', {}).get('items', [])
                
                if items:
                    return items[0].get('item', {}).get('id')
            
            return None
            
        except Exception as e:
            logging.error(f"Error finding deal by address: {str(e)}")
            return None

    def check_sms_delivery_status(self):
        """Check delivery status of sent SMS messages"""
        try:
            print(f"\n📡 Checking SMS delivery status...")
            
            # Check message status via SMS API
            status_url = "https://humilix.com/sms/api.php"
            params = {
                'key': self.sms_token,
                'action': 'get_status',
                'format': 'json'
            }
            
            response = requests.get(status_url, params=params, timeout=15)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"📊 SMS Status Response: {json.dumps(data, indent=2)}")
                    return data
                except json.JSONDecodeError:
                    print(f"⚠️ Non-JSON response: {response.text}")
                    return None
            else:
                print(f"❌ Status check failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error checking SMS status: {str(e)}")
            return None

    def check_incoming_responses(self):
        """Check for incoming SMS responses from Pakistani device"""
        try:
            print(f"\n📱 Checking for incoming SMS responses from OP5312L1...")

            # Method 1: Try receive endpoint with specific device
            receive_url = "https://humilix.com/sms/services/receive.php"
            params = {
                'key': self.sms_token,
                'device': '9',  # OP5312L1 device
                'limit': '50'
            }
            
            response = requests.get(receive_url, params=params, timeout=15)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        messages = data.get('data', [])
                        print(f"📨 Found {len(messages)} incoming messages")
                        return messages
                    else:
                        print(f"⚠️ API returned success=false: {data.get('error', 'Unknown error')}")
                except json.JSONDecodeError:
                    print(f"⚠️ Non-JSON response: {response.text[:200]}...")
            
            # Method 2: Try alternative endpoint
            alt_url = "https://humilix.com/sms/api.php"
            alt_params = {
                'key': self.sms_token,
                'action': 'inbox',
                'format': 'json'
            }
            
            alt_response = requests.get(alt_url, params=alt_params, timeout=15)
            
            if alt_response.status_code == 200:
                try:
                    alt_data = alt_response.json()
                    if isinstance(alt_data, list):
                        print(f"📨 Found {len(alt_data)} messages via alt method")
                        return alt_data
                    elif isinstance(alt_data, dict):
                        messages = alt_data.get('messages', alt_data.get('data', []))
                        print(f"📨 Found {len(messages)} messages via alt dict")
                        return messages
                except json.JSONDecodeError:
                    print(f"⚠️ Alt method non-JSON: {alt_response.text[:200]}...")
            
            print(f"ℹ️ No incoming messages found")
            return []
            
        except Exception as e:
            print(f"❌ Error checking incoming responses: {str(e)}")
            return []

    def process_response(self, message):
        """Process an incoming SMS response"""
        try:
            sender_phone = message.get('from', message.get('number', ''))
            message_text = message.get('message', message.get('text', ''))
            message_id = message.get('id', '')
            received_time = message.get('timestamp', datetime.now().isoformat())
            
            print(f"\n📞 Processing response:")
            print(f"   From: {sender_phone}")
            print(f"   Message: {message_text}")
            print(f"   ID: {message_id}")
            
            # Skip if already processed
            if message_id in self.processed_responses:
                print(f"   ⚠️ Already processed")
                return False
            
            # Find corresponding sent message
            sent_info = self.sent_messages.get(sender_phone)
            
            if sent_info:
                deal_id = sent_info['deal_id']
                address = sent_info['address']
                agent_name = sent_info['agent_name']
                
                print(f"   ✅ Found matching deal: {deal_id}")
                print(f"   🏠 Property: {address}")
                print(f"   👤 Agent: {agent_name}")
                
                # Update deal to Responded stage
                self.update_deal_to_responded(deal_id, message_text, received_time, sender_phone)
                
                # Auto-response disabled per client request
                # self.send_auto_response(sender_phone, message_text)

                # Add response note
                self.add_response_note(deal_id, sender_phone, agent_name, message_text, received_time)

                # Mark as processed
                self.processed_responses.add(message_id)
                
                # Update sent message status
                sent_info['status'] = 'responded'
                sent_info['response_time'] = received_time
                sent_info['response_text'] = message_text
                
                print(f"   🎉 Response processed successfully!")
                return True
            else:
                print(f"   ⚠️ No matching sent message found for {sender_phone}")
                return False
                
        except Exception as e:
            print(f"❌ Error processing response: {str(e)}")
            return False

    def update_deal_to_responded(self, deal_id, message_text, received_time, sender_phone):
        """Update deal stage to Responded"""
        try:
            update_data = {
                'stage_id': self.stages['RESPONDED']  # Stage 116
            }
            
            url = f"{self.base_url}/deals/{deal_id}?api_token={self.pipedrive_token}"
            response = requests.put(url, json=update_data)
            
            if response.status_code == 200:
                print(f"   ✅ Deal {deal_id} moved to Responded stage")
                return True
            else:
                print(f"   ❌ Failed to update deal stage: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating deal stage: {str(e)}")
            return False

    def add_response_note(self, deal_id, sender_phone, agent_name, message_text, received_time):
        """Add response note to Pipedrive deal"""
        try:
            note_content = f"""
📞 AGENT RESPONSE RECEIVED

Agent: {agent_name}
Phone: {sender_phone}
Received: {received_time}

Agent Response:
{message_text}
            """.strip()
            
            note_data = {
                'content': note_content,
                'deal_id': deal_id
            }
            
            url = f"{self.base_url}/notes?api_token={self.pipedrive_token}"
            response = requests.post(url, json=note_data)
            
            if response.status_code == 201:
                print(f"   ✅ Response note added to deal {deal_id}")
                return True
            else:
                print(f"   ❌ Failed to add response note: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error adding response note: {str(e)}")
            return False

    def send_auto_response(self, target_phone, original_message=""):
        """Send automatic response using confirmed working configuration"""
        try:
            print(f"📤 Sending auto-response to {target_phone}")

            # Create personalized auto-response
            if original_message:
                auto_message = f"Thank you for your response! Your message '{original_message}' was received. This is an automated reply. We will contact you shortly with more details about the property."
            else:
                auto_message = "Thank you for your response! This is an automated reply from our real estate system. We have received your message and will get back to you shortly."

            # Use confirmed working SMS configuration
            sms_data = {
                'key': self.sms_token,
                'number': target_phone,
                'message': auto_message,
                'devices': self.working_device_config,  # 9|1 - CONFIRMED WORKING
                'type': 'sms',
                'prioritize': '0'
            }

            print(f"💬 Auto-response: {auto_message}")
            print(f"🔧 Using device config: {self.working_device_config}")

            # Send auto-response
            response = requests.post(self.sms_api_url, data=sms_data, timeout=30)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        msg_info = result.get('data', {}).get('messages', [{}])[0]
                        response_id = msg_info.get('ID')
                        device_id = msg_info.get('deviceID')
                        sim_slot = msg_info.get('simSlot')

                        print(f"✅ Auto-response sent successfully!")
                        print(f"   Response ID: {response_id}")
                        print(f"   Device ID: {device_id}")
                        print(f"   SIM Slot: {sim_slot}")

                        return response_id
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        print(f"❌ Auto-response failed: {error_msg}")
                        return None
                except json.JSONDecodeError:
                    print(f"⚠️ Auto-response sent (non-JSON response)")
                    return True
            else:
                print(f"❌ Auto-response HTTP error: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ Error sending auto-response: {str(e)}")
            return None

    def run_response_tracking_cycle(self):
        """Run one cycle of response tracking"""
        print(f"\n🔄 Running response tracking cycle...")
        print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check SMS delivery status
        delivery_status = self.check_sms_delivery_status()
        
        # Check for incoming responses
        incoming_messages = self.check_incoming_responses()
        
        # Process each incoming message
        processed_count = 0
        for message in incoming_messages:
            if self.process_response(message):
                processed_count += 1
        
        print(f"\n📊 Cycle Summary:")
        print(f"   📨 Incoming messages: {len(incoming_messages)}")
        print(f"   ✅ Processed responses: {processed_count}")
        print(f"   📋 Total tracked messages: {len(self.sent_messages)}")
        
        return processed_count

    def start_continuous_tracking(self, check_interval=60):
        """Start continuous response tracking"""
        print(f"\n🚀 Starting continuous response tracking...")
        print(f"⏱️ Check interval: {check_interval} seconds")
        print(f"📱 Tracking responses for {len(self.sent_messages)} sent messages")
        print(f"🔄 Press Ctrl+C to stop")
        
        try:
            while True:
                self.run_response_tracking_cycle()
                
                print(f"\n⏳ Waiting {check_interval} seconds before next check...")
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Response tracking stopped by user")
        except Exception as e:
            print(f"\n❌ Error in continuous tracking: {str(e)}")

def main():
    """Main function"""
    tracker = EnhancedResponseTracker()
    
    print(f"\n📱 Enhanced Response Tracker Options:")
    print(f"1. Run single tracking cycle")
    print(f"2. Start continuous tracking (60 sec intervals)")
    print(f"3. Start continuous tracking (30 sec intervals)")
    print(f"4. Check SMS delivery status only")
    print(f"5. Check incoming messages only")
    
    choice = input(f"\nEnter choice (1-5): ").strip()
    
    if choice == '1':
        tracker.run_response_tracking_cycle()
    elif choice == '2':
        tracker.start_continuous_tracking(60)
    elif choice == '3':
        tracker.start_continuous_tracking(30)
    elif choice == '4':
        tracker.check_sms_delivery_status()
    elif choice == '5':
        tracker.check_incoming_responses()
    else:
        print(f"Invalid choice")

if __name__ == "__main__":
    main()
