#!/usr/bin/env python3
"""
Multi-Device Manager for SMS System
Manages multiple devices and phone numbers for SMS sending
"""

import json
import random
import time
import logging
from datetime import datetime, timedelta

class MultiDeviceManager:
    def __init__(self, config_file='multi_device_config.json'):
        self.config_file = config_file
        self.devices = []
        self.current_device_index = 0
        self.device_usage = {}  # Track usage per device
        self.load_config()
        
        logging.info(f"📱 Multi-Device Manager initialized with {len(self.devices)} devices")

    def load_config(self):
        """Load multi-device configuration"""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            self.devices = [d for d in config.get('devices', []) if d.get('enabled', True)]
            self.load_balancing = config.get('load_balancing', 'round_robin')
            self.fallback_enabled = config.get('fallback_enabled', True)
            
            # Initialize usage tracking
            for device in self.devices:
                device_key = f"{device['device_id']}|{device['sim_slot']}"
                self.device_usage[device_key] = {
                    'messages_sent': 0,
                    'last_used': None,
                    'hourly_count': 0,
                    'hour_start': datetime.now().replace(minute=0, second=0, microsecond=0)
                }
            
            logging.info(f"✅ Loaded {len(self.devices)} devices from config")
            
        except Exception as e:
            logging.error(f"❌ Error loading device config: {str(e)}")
            # Fallback to client device
            self.devices = [{
                'device_id': '10',
                'device_name': 'Galaxy Note9',
                'sim_slot': '0',
                'phone_number': '+19542547716',
                'config_string': '10|0',
                'priority': 1,
                'enabled': True,
                'max_messages_per_hour': 100
            }]

    def get_next_device(self, target_phone=None):
        """Get next device based on load balancing strategy"""
        try:
            available_devices = self.get_available_devices()
            
            if not available_devices:
                logging.warning("⚠️ No available devices, using fallback")
                return self.devices[0] if self.devices else None
            
            if self.load_balancing == 'round_robin':
                return self.get_round_robin_device(available_devices)
            elif self.load_balancing == 'random':
                return random.choice(available_devices)
            elif self.load_balancing == 'priority':
                return self.get_priority_device(available_devices)
            else:
                return available_devices[0]
                
        except Exception as e:
            logging.error(f"❌ Error getting next device: {str(e)}")
            return self.devices[0] if self.devices else None

    def get_available_devices(self):
        """Get devices that haven't exceeded hourly limits"""
        available = []
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        for device in self.devices:
            if not device.get('enabled', True):
                continue
                
            device_key = f"{device['device_id']}|{device['sim_slot']}"
            usage = self.device_usage.get(device_key, {})
            
            # Reset hourly counter if new hour
            if usage.get('hour_start') != current_hour:
                usage['hourly_count'] = 0
                usage['hour_start'] = current_hour
                self.device_usage[device_key] = usage
            
            # Check if under hourly limit
            max_per_hour = device.get('max_messages_per_hour', 100)
            if usage.get('hourly_count', 0) < max_per_hour:
                available.append(device)
        
        return available

    def get_round_robin_device(self, available_devices):
        """Get next device using round robin"""
        if self.current_device_index >= len(available_devices):
            self.current_device_index = 0
        
        device = available_devices[self.current_device_index]
        self.current_device_index += 1
        return device

    def get_priority_device(self, available_devices):
        """Get highest priority available device"""
        return min(available_devices, key=lambda d: d.get('priority', 999))

    def mark_device_used(self, device):
        """Mark device as used and update usage stats"""
        try:
            device_key = f"{device['device_id']}|{device['sim_slot']}"
            usage = self.device_usage.get(device_key, {})
            
            usage['messages_sent'] = usage.get('messages_sent', 0) + 1
            usage['last_used'] = datetime.now().isoformat()
            usage['hourly_count'] = usage.get('hourly_count', 0) + 1
            
            self.device_usage[device_key] = usage
            
            logging.info(f"📱 Device {device_key} used - Total: {usage['messages_sent']}, Hourly: {usage['hourly_count']}")
            
        except Exception as e:
            logging.error(f"❌ Error marking device used: {str(e)}")

    def get_device_config_string(self, device):
        """Get device configuration string for SMS API"""
        return device.get('config_string', f"{device['device_id']}|{device['sim_slot']}")

    def get_sender_phone(self, device):
        """Get sender phone number for device"""
        return device.get('phone_number', 'Unknown')

    def get_usage_stats(self):
        """Get usage statistics for all devices"""
        stats = {
            'total_devices': len(self.devices),
            'enabled_devices': len([d for d in self.devices if d.get('enabled', True)]),
            'device_stats': {}
        }
        
        for device in self.devices:
            device_key = f"{device['device_id']}|{device['sim_slot']}"
            usage = self.device_usage.get(device_key, {})
            
            stats['device_stats'][device_key] = {
                'device_name': device.get('device_name', 'Unknown'),
                'phone_number': device.get('phone_number', 'Unknown'),
                'messages_sent': usage.get('messages_sent', 0),
                'hourly_count': usage.get('hourly_count', 0),
                'max_per_hour': device.get('max_messages_per_hour', 100),
                'last_used': usage.get('last_used'),
                'enabled': device.get('enabled', True)
            }
        
        return stats

    def reset_hourly_counters(self):
        """Reset hourly counters for all devices"""
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        for device_key in self.device_usage:
            self.device_usage[device_key]['hourly_count'] = 0
            self.device_usage[device_key]['hour_start'] = current_hour
        
        logging.info("🔄 Hourly counters reset for all devices")

    def get_best_device_for_target(self, target_phone):
        """Get best device for specific target phone number"""
        # For Pakistani numbers, prefer Pakistani devices
        if target_phone.startswith('+92'):
            pakistani_devices = [d for d in self.devices if d.get('phone_number', '').startswith('+92')]
            if pakistani_devices:
                available_pak = [d for d in pakistani_devices if d in self.get_available_devices()]
                if available_pak:
                    return self.get_priority_device(available_pak)
        
        # For US numbers, prefer US devices
        elif target_phone.startswith('+1'):
            us_devices = [d for d in self.devices if d.get('phone_number', '').startswith('+1')]
            if us_devices:
                available_us = [d for d in us_devices if d in self.get_available_devices()]
                if available_us:
                    return self.get_priority_device(available_us)
        
        # Fallback to any available device
        return self.get_next_device()

def test_multi_device_manager():
    """Test the multi-device manager"""
    print(f"\n🧪 Testing Multi-Device Manager")
    print(f"="*50)
    
    manager = MultiDeviceManager()
    
    # Test getting devices
    for i in range(5):
        device = manager.get_next_device()
        if device:
            config = manager.get_device_config_string(device)
            phone = manager.get_sender_phone(device)
            
            print(f"Device {i+1}: {config} ({phone})")
            manager.mark_device_used(device)
        else:
            print(f"Device {i+1}: No device available")
    
    # Show usage stats
    stats = manager.get_usage_stats()
    print(f"\n📊 Usage Statistics:")
    print(json.dumps(stats, indent=2))

if __name__ == "__main__":
    test_multi_device_manager()
