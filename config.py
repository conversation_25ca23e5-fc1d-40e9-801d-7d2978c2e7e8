#!/usr/bin/env python3
"""
Configuration file for Integrated Zillow System
"""

# API Keys and Tokens
SCRAPER_API_KEY = '91b405af27e201a62e346fefbdca9664'
PIPEDRIVE_TOKEN = '****************************************'
SMS_API_TOKEN = '619bfcdb57e507d6fa1c96f836db7215b454af9d'

# API URLs
SMS_API_URL = 'https://humilix.com/sms/services/send.php'
SMS_INCOMING_URL = 'https://humilix.com/sms/api.php'
SMS_RECEIVE_URL = 'https://humilix.com/sms/services/receive.php'
PIPEDRIVE_BASE_URL = 'https://api.pipedrive.com/v1'

# SMS API Configuration
SMS_CONFIG = {
    'SEND_ENDPOINT': 'https://humilix.com/sms/services/send.php',
    'RECEIVE_ENDPOINT': 'https://humilix.com/sms/services/receive.php',
    'API_ENDPOINT': 'https://humilix.com/sms/api.php',
    'TOKEN': SMS_API_TOKEN,
    'SEND_METHOD': 'GET',  # Using GET method as per your endpoint
    'SEND_PARAMS': {
        'key': SMS_API_TOKEN,
        'devices': '["9|0","9|1"]',  # Device configuration from your example
        'type': 'sms',
        'prioritize': '0'
    },
    'RECEIVE_PARAMS': {
        'key': SMS_API_TOKEN,
        'action': 'get_messages'
    }
}

# Pipedrive Configuration
PIPEDRIVE_PIPELINE_ID = '9'
PIPEDRIVE_STAGES = {
    'LEAD_IN': 101,              # Lead In - Initial stage (CSV data added)
    'CONTACT_ATTEMPTED': 102,    # Contact Attempted - After SMS sent to agent
    'NO_RESPONSE': 109,          # No Response - Agent didn't respond
    'RESPONDED': 116,            # Responded - After agent responds
    'FOLLOW_UP_14': 110,         # 14 Day Follow Up
    'FOLLOW_UP_30': 111,         # 30 Day Follow Up
    'FOLLOW_UP_60': 113,         # 60 Day Follow Up
    'FOLLOW_UP_90': 112,         # 90 Day Follow Up
    'NEGOTIATION': 104,          # Negotiation CA or CR
    'PENDING': 114,              # Pending
    'CONTRACT_SENT': 105,        # Contract Sent
    'CONTRACT_SIGNED': 106,      # Contract Signed
    'CLOSED': 107                # Closed - Deal completed
}

# Phone Configuration
CLIENT_PHONE = '+13214304820'  # Your client's phone number for SMS sending
SENDER_PHONE = '+13214304820'  # Phone number that will appear as sender

# Message Templates (Customizable)
MESSAGE_TEMPLATES = {
    'CASH_OFFER_TEMPLATE': """Hello! Inquiring on {address}. I'm Prepared to offer {offer_amount} all cash, quick close. Would it make sense to write up an offer? Please advise on next steps."""
}

# Message Configuration
MESSAGE_CONFIG = {
    'DEFAULT_TEMPLATE': 'CASH_OFFER_TEMPLATE',
    'ALLOW_CUSTOM_INPUT': True,
    'INCLUDE_SENDER_INFO': True,
    'MAX_MESSAGE_LENGTH': 160,
    'OFFER_PERCENTAGE': 65,  # Default offer percentage (65% of listing price)
    'PERSONALIZATION': {
        'USE_AGENT_NAME': True,
        'USE_PROPERTY_ADDRESS': True,
        'USE_PROPERTY_PRICE': True,
        'USE_SENDER_NAME': True,
        'USE_OFFER_CALCULATION': True
    }
}

# Scraping Configuration
SCRAPING_CONFIG = {
    'MAX_PAGES_PER_BATCH': 10,
    'DELAY_BETWEEN_PAGES': 3,
    'DELAY_BETWEEN_BATCHES': 30,
    'MAX_PROPERTIES_PER_RUN': 1000,
    'RETRY_ATTEMPTS': 3,
    'REQUEST_TIMEOUT': 90
}

# Search Filters for Zillow
ZILLOW_FILTERS = {
    "price": {"min": 100000, "max": 400000},
    "mp": {"min": 504, "max": 2015},
    "beds": {"min": 2},
    "baths": {"min": 2},
    "gar": {"value": True},
    "sqft": {"min": 1000},
    "lot": {"min": 0, "max": 43560},
    "built": {"min": 1970},
    "55plus": {"value": "e"},
    "att": {"value": "AS-IS"}
}

# Geographic bounds for Florida
FLORIDA_BOUNDS = {
    "west": -96.44986467187502,
    "east": -71.15933732812502,
    "south": 20.393022319423494,
    "north": 34.639525177290345
}

# Threading Configuration
THREAD_CONFIG = {
    'SCRAPER_THREADS': 2,
    'PROCESSOR_THREADS': 3,
    'SMS_THREADS': 1,
    'MONITOR_THREADS': 1,
    'QUEUE_TIMEOUT': 30
}

# File Configuration
FILES = {
    'CSV_FILE': 'zillow_florida_data.csv',
    'LOG_FILE': 'zillow_system.log',
    'BACKUP_DIR': 'backups',
    'TEMP_DIR': 'temp'
}

# CSV Headers
CSV_HEADERS = ['Address', 'Beds', 'Baths', 'Sqft', 'Price', 'Agent_Name', 'Agent_Phone', 'URL']

# Validation Rules
VALIDATION_RULES = {
    'BEDS_RANGE': (1, 20),
    'BATHS_RANGE': (1, 20),
    'SQFT_RANGE': (500, 50000),
    'PRICE_RANGE': (50000, 5000000),
    'AGENT_NAME_MIN_LENGTH': 3,
    'PHONE_FORMATS': [
        r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',  # US format
        r'\+1\d{10}',  # International US format
    ]
}

# System Settings
SYSTEM_SETTINGS = {
    'AUTO_START': True,
    'ENABLE_LOGGING': True,
    'LOG_LEVEL': 'INFO',
    'BACKUP_FREQUENCY': 3600,  # seconds
    'STATUS_UPDATE_INTERVAL': 60,  # seconds
    'HEALTH_CHECK_INTERVAL': 300,  # seconds
}

# Error Handling
ERROR_HANDLING = {
    'MAX_RETRIES': 3,
    'RETRY_DELAY': 5,
    'CIRCUIT_BREAKER_THRESHOLD': 10,
    'CIRCUIT_BREAKER_TIMEOUT': 300
}

# Rate Limiting
RATE_LIMITS = {
    'SCRAPER_API_DELAY': 2,
    'PIPEDRIVE_API_DELAY': 1,
    'SMS_API_DELAY': 3,
    'MAX_REQUESTS_PER_MINUTE': 30
}

# Pipedrive API Limit Management
PIPEDRIVE_LIMITS = {
    'DAILY_LIMIT': 10000,           # 10,000 requests per day
    'HOURLY_LIMIT': 417,            # ~10000/24 requests per hour
    'MINUTE_LIMIT': 7,              # ~417/60 requests per minute
    'BATCH_SIZE': 50,               # Process in batches
    'PRIORITY_OPERATIONS': [        # High priority operations
        'create_deal',
        'add_note',
        'update_stage'
    ],
    'LOW_PRIORITY_OPERATIONS': [    # Low priority operations
        'search_deals',
        'get_deal_details'
    ],
    'RATE_LIMIT_BUFFER': 0.8,      # Use 80% of limit to be safe
    'RESET_TIME': '00:00:00'        # Daily reset time (UTC)
}

# Monitoring and Alerts
MONITORING = {
    'ENABLE_ALERTS': True,
    'ALERT_EMAIL': '<EMAIL>',
    'ALERT_THRESHOLDS': {
        'QUEUE_SIZE_WARNING': 100,
        'QUEUE_SIZE_CRITICAL': 500,
        'ERROR_RATE_WARNING': 0.1,
        'ERROR_RATE_CRITICAL': 0.25
    }
}

# Development/Testing Settings
DEV_SETTINGS = {
    'TEST_MODE': False,
    'TEST_PHONE': '+13214304820',
    'MOCK_SMS': False,
    'MOCK_PIPEDRIVE': False,
    'LIMITED_SCRAPING': False,
    'MAX_TEST_PROPERTIES': 10
}

def get_config():
    """Get complete configuration dictionary"""
    return {
        'api_keys': {
            'scraper': SCRAPER_API_KEY,
            'pipedrive': PIPEDRIVE_TOKEN,
            'sms': SMS_API_TOKEN
        },
        'urls': {
            'sms_send': SMS_API_URL,
            'sms_incoming': SMS_INCOMING_URL,
            'pipedrive': PIPEDRIVE_BASE_URL
        },
        'pipedrive': {
            'pipeline_id': PIPEDRIVE_PIPELINE_ID,
            'stages': PIPEDRIVE_STAGES
        },
        'phone': {
            'client': CLIENT_PHONE
        },
        'messages': MESSAGE_TEMPLATES,
        'scraping': SCRAPING_CONFIG,
        'filters': ZILLOW_FILTERS,
        'bounds': FLORIDA_BOUNDS,
        'threads': THREAD_CONFIG,
        'files': FILES,
        'headers': CSV_HEADERS,
        'validation': VALIDATION_RULES,
        'system': SYSTEM_SETTINGS,
        'errors': ERROR_HANDLING,
        'rates': RATE_LIMITS,
        'monitoring': MONITORING,
        'dev': DEV_SETTINGS
    }

def validate_config():
    """Validate configuration settings"""
    errors = []
    
    # Check required API keys
    if not SCRAPER_API_KEY:
        errors.append("SCRAPER_API_KEY is required")
    
    if not PIPEDRIVE_TOKEN:
        errors.append("PIPEDRIVE_TOKEN is required")
    
    if not SMS_API_TOKEN:
        errors.append("SMS_API_TOKEN is required")
    
    # Check phone number format
    import re
    if CLIENT_PHONE and not re.match(r'\+\d{11,15}', CLIENT_PHONE):
        errors.append("CLIENT_PHONE must be in international format (+1234567890)")
    
    # Check numeric ranges
    if SCRAPING_CONFIG['MAX_PAGES_PER_BATCH'] < 1:
        errors.append("MAX_PAGES_PER_BATCH must be at least 1")
    
    if THREAD_CONFIG['SCRAPER_THREADS'] < 1:
        errors.append("SCRAPER_THREADS must be at least 1")
    
    return errors

if __name__ == "__main__":
    # Validate configuration
    errors = validate_config()
    
    if errors:
        print("❌ Configuration Errors:")
        for error in errors:
            print(f"   - {error}")
    else:
        print("✅ Configuration is valid")
        
    # Print configuration summary
    config = get_config()
    print(f"\n📋 Configuration Summary:")
    print(f"   🔑 API Keys: {len([k for k in config['api_keys'].values() if k])}/3 configured")
    print(f"   📱 Client Phone: {config['phone']['client']}")
    print(f"   🧵 Threads: {sum(config['threads'].values())} total")
    print(f"   📄 CSV File: {config['files']['CSV_FILE']}")
    print(f"   🎯 Max Properties: {config['scraping']['MAX_PROPERTIES_PER_RUN']}")
